// src/hooks/useProductMatching.ts
import { useState, useEffect } from "react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { OCRProduct, MatchedProduct, Product } from "@/types/product";

export const useProductMatching = (ocrProducts: OCRProduct[]) => {
  const [matchedProducts, setMatchedProducts] = useState<MatchedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  // Process OCR products when they change
  useEffect(() => {
    if (ocrProducts.length > 0) {
      processProductMatching();
    } else {
      setMatchedProducts([]);
    }
  }, [ocrProducts]);

  const searchProducts = async (searchTerm: string): Promise<Product[]> => {
    try {
      const response = await apiCall<{ results: Product[] }>(
        'GET',
        `${endpoints.products.list}?search=${encodeURIComponent(searchTerm)}&page_size=10`
      );
      return response.results || [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  };

  const normalizeProductName = (name: string): string => {
    return name.toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
  };

  const calculateSimilarity = (str1: string, str2: string): number => {
    // Simple Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  };

  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  };

  const findBestMatch = async (ocrProductName: string): Promise<{ product: Product | null; confidence: 'high' | 'medium' | 'low' | 'none' }> => {
    const normalizedOcrName = normalizeProductName(ocrProductName);
    
    // First, search using the API with the original OCR product name
    const searchResults = await searchProducts(ocrProductName);
    
    let bestMatch: Product | null = null;
    let bestScore = 0;
    let confidence: 'high' | 'medium' | 'low' | 'none' = 'none';

    // If we have search results, process them
    if (searchResults.length > 0) {
      for (const product of searchResults) {
        // Check exact match with product name
        const normalizedProductName = normalizeProductName(product.name);
        if (normalizedProductName === normalizedOcrName) {
          return { product, confidence: 'high' };
        }

        // Check other names for exact match
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const normalizedOtherName = normalizeProductName(otherName);
            if (normalizedOtherName === normalizedOcrName) {
              return { product, confidence: 'high' };
            }
          }
        }

        // Calculate similarity score with product name
        const score = calculateSimilarity(normalizedOcrName, normalizedProductName);
        if (score > bestScore) {
          bestScore = score;
          bestMatch = product;
        }

        // Check similarity with other names
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const otherScore = calculateSimilarity(normalizedOcrName, normalizeProductName(otherName));
            if (otherScore > bestScore) {
              bestScore = otherScore;
              bestMatch = product;
            }
          }
        }
      }
    }

    // If no good match found in search results, try searching with normalized name
    if (bestScore < 0.6 && normalizedOcrName !== ocrProductName) {
      const fallbackResults = await searchProducts(normalizedOcrName);
      
      for (const product of fallbackResults) {
        const normalizedProductName = normalizeProductName(product.name);
        const score = calculateSimilarity(normalizedOcrName, normalizedProductName);
        
        if (score > bestScore) {
          bestScore = score;
          bestMatch = product;
        }

        // Check other names
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const otherScore = calculateSimilarity(normalizedOcrName, normalizeProductName(otherName));
            if (otherScore > bestScore) {
              bestScore = otherScore;
              bestMatch = product;
            }
          }
        }
      }
    }

    // Determine confidence based on score
    if (bestScore >= 0.8) {
      confidence = 'high';
    } else if (bestScore >= 0.6) {
      confidence = 'medium';
    } else if (bestScore >= 0.4) {
      confidence = 'low';
    }

    return { product: bestMatch, confidence };
  };

  const processProductMatching = async () => {
    setIsLoading(true);
    try {
      const matched: MatchedProduct[] = [];
      
      for (const ocrProduct of ocrProducts) {
        const match = await findBestMatch(ocrProduct.product);
        matched.push({
          ocrProduct,
          matchedProduct: match.product,
          confidence: match.confidence
        });
      }
      
      setMatchedProducts(matched);
    } catch (error) {
      console.error('Error processing product matching:', error);
      showToast('Có lỗi xảy ra khi tìm kiếm sản phẩm', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const reprocessMatching = () => {
    if (ocrProducts.length > 0) {
      processProductMatching();
    }
  };

  return {
    // State
    matchedProducts,
    isLoading,
    
    // Actions
    reprocessMatching,
  };
};
