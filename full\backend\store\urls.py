from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    CategoryViewSet, ProductViewSet, OrderViewSet,
    RegisterUserView, UserProfileView, CartView,
    CartItemDeleteView, CartItemUpdateView,
    ProductImageViewSet, ProductVariantViewSet,
    VariantImageViewSet, CartPromotionView, BannerViewSet, OrderOCRView
)
# Use the refactored report views
from .views.report_views_refactored import ReportViewSet
from .views.note_views import NoteViewSet
from .views.promotion_views import PromotionViewSet
from .views.shipping_views import ShippingFeeView # Import the new view
from .views.store_location_views import StoreLocationViewSet
from .views.feedback_views import CustomerFeedbackViewSet
from .views.payment_views import PayOSPaymentView, PayOSWebhookView
from .views.sepay_views import SepayPaymentView, SepayWebhookView, SepayPaymentStatusView
from .views.password_reset_views import (
    PasswordResetRequestView,
    PasswordResetConfirmView,
    PasswordRetrievalView
)
from .views.favorite_product_views import FavoriteProductView
from .views.recently_viewed_product_views import RecentlyViewedProductView
from .views.promotional_email_views import ProductPromotionalEmailView
from .views.debug_views import debug_time, debug_order_time
from .views.scraping_views import scrape_inventory_view
from .views.inventory_views import InventoryUpdateLogViewSet

router = DefaultRouter()
router.register(r'categories', CategoryViewSet)
router.register(r'products', ProductViewSet)
router.register(r'product-images', ProductImageViewSet)
router.register(r'product-variants', ProductVariantViewSet)
router.register(r'variant-images', VariantImageViewSet)
router.register(r'orders', OrderViewSet)
router.register(r'banners', BannerViewSet)
router.register(r'store-locations', StoreLocationViewSet)
router.register(r'feedback', CustomerFeedbackViewSet)
router.register(r'promotions', PromotionViewSet)
router.register(r'users', UserProfileView, basename='user')
router.register(r'reports', ReportViewSet, basename='reports')
router.register(r'inventory-logs', InventoryUpdateLogViewSet, basename='inventory-logs')

# Create a nested router for user notes
note_router = DefaultRouter()
note_router.register(r'notes', NoteViewSet, basename='user-notes')

urlpatterns = [
    # Include router URLs directly
    path('', include(router.urls)),

    # User-related endpoints
    path('users/me/', UserProfileView.as_view({'get': 'me', 'patch': 'me'}), name='user-profile'),
    path('users/create-staff/', UserProfileView.as_view({'post': 'create_staff'}), name='create-staff'),
    path('users/change-password/', UserProfileView.as_view({'post': 'change_password'}), name='change-password'),
    path('users/<int:user_id>/', include(note_router.urls)),  # Nested notes URLs
    path('register/', RegisterUserView.as_view(), name='register'),

    # Cart-related endpoints
    path('cart/', CartView.as_view(), name='cart'),
    path('cart/items/<int:pk>/', CartItemUpdateView.as_view(), name='cart-item-update'),
    path('cart/items/<int:pk>/delete/', CartItemDeleteView.as_view(), name='cart-item-delete'),
    path('cart/promotions/', CartPromotionView.as_view(), name='cart-promotions'),
    path('cart/promotions/<int:pk>/', CartPromotionView.as_view(), name='cart-promotion-delete'),

    # Password reset endpoints
    path('password-reset/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password-reset/confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    path('password-retrieval/', PasswordRetrievalView.as_view(), name='password-retrieval'),

    # PayOS payment endpoints
    path('orders/<int:order_id>/payos-payment/', PayOSPaymentView.as_view(), name='payos-payment'),
    path('payos-webhook/', PayOSWebhookView.as_view(), name='payos-webhook'),

    # Sepay payment endpoints
    path('orders/<int:order_id>/sepay-payment/', SepayPaymentView.as_view(), name='sepay-payment'),
    path('orders/<int:order_id>/sepay-status/', SepayPaymentStatusView.as_view(), name='sepay-payment-status'),
    path('sepay-webhook/', SepayWebhookView.as_view(), name='sepay-webhook'),

    # Favorite product endpoints
    path('favorites/', FavoriteProductView.as_view(), name='favorite-products'),
    path('favorites/<int:pk>/', FavoriteProductView.as_view(), name='favorite-product-detail'),

    # Recently viewed product endpoints
    path('recently-viewed/', RecentlyViewedProductView.as_view(), name='recently-viewed-products'),

    # Promotional email endpoint
    path('products/send-promotional-email/', ProductPromotionalEmailView.as_view(), name='send-promotional-email'),

    # Shipping fee calculation endpoint
    path('shipping/calculate-fee/', ShippingFeeView.as_view(), name='calculate-shipping-fee'),

    # Simple scraping endpoint
    path('scrape_inventory/', scrape_inventory_view, name='scrape-inventory'),

    # Order OCR endpoint
    path('order-ocr/', OrderOCRView.as_view(), name='order-ocr'),

    # Debug endpoints
    # path('debug/time/', debug_time, name='debug-time'),
    # path('debug/order-time/', debug_order_time, name='debug-order-time'),
    # path('debug/order-time/<int:order_id>/', debug_order_time, name='debug-order-time-detail'),
]
