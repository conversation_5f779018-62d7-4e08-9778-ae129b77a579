import { useState, useEffect, useMemo, useRef } from "react";
import { Order, OrderItem } from "@/types/order";
import { formatCurrency } from "@/lib/utils";
import { Button, InputNumber, Table } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { CustomerService } from "@/services/customerService";
import { calculateTotalWeight, formatWeight } from "@/utils/weightUtils";
import { useAuth } from "@/context/auth-hooks";

interface OrderItemsSectionProps {
  order: Order;
  onUpdateOrder: (data: Partial<Order>) => void;
  onAddProduct?: () => void;
  editingItem: number | null;
  onEditItem?: (id: number | null) => void;
  disabled?: boolean;
  isMobile: boolean;
  isTablet?: boolean;
}

type EditingField = "quantity" | "total_price" | "chiet_khau_amount" | null;

export function OrderItemsSection({
  order,
  onUpdateOrder,
  onAddProduct,
  editingItem,
  onEditItem,
  disabled = false,
  isMobile,
  isTablet = false,
}: OrderItemsSectionProps) {
  const { user } = useAuth();
  const [editingField, setEditingField] = useState<EditingField>(null);

  // Check if user can edit product prices (sales_admin cannot edit prices)
  const canEditPrice = user?.role !== "sales_admin";

  const [orderItems, setOrderItems] = useState<OrderItem[]>(order.items);
  const [shippingFee, setShippingFee] = useState(order.shipping_fee || 0);
  const [discount, setDiscount] = useState(order.discount || 0);
  const [tax, setTax] = useState(order.tax || 0);

  const isInitialLoad = useRef(true);

  useEffect(() => {
    if (isInitialLoad.current) {
      setOrderItems(order.items);
      setShippingFee(order.shipping_fee || 0);
      setDiscount(order.discount || 0);
      setTax(order.tax || 0);
      isInitialLoad.current = false;
    } else {
      setOrderItems(order.items);

      setShippingFee(order.shipping_fee || 0);
      setDiscount(order.discount || 0);
      setTax(order.tax || 0);
    }
  }, [order]);

  const subtotal = useMemo(() => {
    return orderItems.reduce(
      (sum, item) => sum + Number(item.total_price || 0),
      0
    );
  }, [orderItems]);

  const totalWeight = useMemo(() => {
    return calculateTotalWeight(orderItems);
  }, [orderItems]);

  const totalDiscount = useMemo(() => {
    return orderItems.reduce(
      (sum, item) => sum + Number(item.chiet_khau_amount || 0),
      0
    );
  }, [orderItems]);

  const finalTotal = useMemo(() => {
    const subTotal = Math.round(Number(subtotal) || 0);
    const shipping = Math.round(Number(shippingFee) || 0);
    const disc = Math.round(Number(discount) || 0);
    const taxRate = Number(tax) || 0;

    const discountedAmount = Math.max(0, subTotal - disc);
    const taxedAmount = discountedAmount * (1 + taxRate);
    return taxedAmount + shipping;
  }, [subtotal, shippingFee, discount, tax]);

  useEffect(() => {
    if (disabled) return;

    const updatedOrder: Partial<Order> = {
      items: orderItems,
      shipping_fee: shippingFee,
      discount: discount,
      tax: tax,
      total_price: Math.round(finalTotal),
    };

    onUpdateOrder(updatedOrder);
  }, [
    orderItems,
    shippingFee,
    discount,
    tax,
    finalTotal,
    onUpdateOrder,
    disabled,
  ]);

  const handleQuantityChange = (item: OrderItem, newQuantity: number) => {
    if (newQuantity <= 0 || disabled) return;

    setOrderItems((prevItems) =>
      prevItems.map((prevItem) => {
        if (prevItem.id === item.id) {
          const chietKhauAmount = prevItem.chiet_khau_amount || 0;
          const newTotalPrice = Math.max(0, newQuantity * prevItem.price - chietKhauAmount);
          return {
            ...prevItem,
            quantity: newQuantity,
            total_price: newTotalPrice,
          };
        }
        return prevItem;
      })
    );
  };

  const handleTotalPriceChange = (item: OrderItem, newTotalPrice: number) => {
    if (newTotalPrice < 0 || disabled) return;

    setOrderItems((prevItems) =>
      prevItems.map((prevItem) => {
        if (prevItem.id === item.id) {
          const newUnitPrice = newTotalPrice / prevItem.quantity;
          return {
            ...prevItem,
            total_price: newTotalPrice,
            price: Math.round(newUnitPrice),
          };
        }
        return prevItem;
      })
    );
  };

  const handleChietKhauChange = (item: OrderItem, newChietKhauAmount: number) => {
    if (newChietKhauAmount < 0 || disabled) return;

    setOrderItems((prevItems) =>
      prevItems.map((prevItem) => {
        if (prevItem.id === item.id) {
          const newTotalPrice = Math.max(0, prevItem.quantity * prevItem.price - newChietKhauAmount);
          return {
            ...prevItem,
            chiet_khau_amount: newChietKhauAmount,
            total_price: newTotalPrice,
          };
        }
        return prevItem;
      })
    );
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: number) => {
    if (disabled) return;

    // Filter out the removed item
    // We need to be careful with items that have id=0 (newly added items from the server)
    // We'll use a more precise comparison to ensure we only remove the exact item
    const updatedItems = orderItems.filter((item, index, array) => {
      // If this is not the item with the ID we're looking for, keep it
      if (item.id !== itemId) return true;

      // If we're here, this item has the ID we're looking for
      // If this is a regular item (positive ID), we can safely remove it
      if (itemId > 0) return false;

      // For items with ID <= 0 (temporary IDs), we need to be more careful
      // Find the index of this item in the array
      const itemIndex = array.findIndex((i) => i.id === itemId);

      // Only remove if this is the exact item at that index
      return index !== itemIndex;
    });

    setOrderItems(updatedItems);

    const newSubtotal = updatedItems.reduce(
      (sum, item) => sum + (item.total_price || 0),
      0
    );

    onUpdateOrder({
      items: updatedItems,
      total_price: Math.round(newSubtotal * (1 + tax) + shippingFee - discount),
    });
  };

  return (
    <div className="mb-6 bg-white rounded-lg border overflow-hidden">
      <div className="p-6 pb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold">Chi tiết đơn hàng</h3>
        {onAddProduct && (
          <Button type="primary" onClick={onAddProduct} icon={<PlusOutlined />}>
            {!isMobile && "Thêm sản phẩm"}
          </Button>
        )}
      </div>
      <div
        className={isMobile ? "overflow-x-auto" : ""}
        style={isMobile ? { maxWidth: "100%" } : {}}
      >
        <Table
          scroll={isMobile ? { x: 1100 } : undefined}
          dataSource={orderItems}
          pagination={false}
          rowKey="id"
          columns={[
            {
              title: "Mã hàng",
              dataIndex: "product_code",
              key: "product_code",
              width: isMobile ? 80 : 120,
              fixed: isMobile ? "left" : undefined,
              render: (code: string) => (
                <div className="text-sm text-gray-500">{code || "N/A"}</div>
              ),
            },
            {
              title: "Sản phẩm",
              dataIndex: "product_name",
              key: "product_name",
              width: isMobile ? 200 : 250,
              render: (text, record: OrderItem) => (
                <div>
                  <div className="font-medium">{text}</div>
                  {record.variant_name && (
                    <div className="text-sm text-gray-500">
                      {record.variant_name}
                    </div>
                  )}
                </div>
              ),
            },
            {
              title: "Số lượng",
              dataIndex: "quantity",
              key: "quantity",
              render: (_, record: OrderItem) =>
                editingItem === record.id ? (
                  <InputNumber
                    min={1}
                    value={record.quantity}
                    onChange={(value) =>
                      handleQuantityChange(record, Number(value))
                    }
                    onBlur={() => {
                      setEditingField(null);
                      onEditItem?.(null);
                    }}
                    autoFocus={editingField === "quantity"}
                    disabled={disabled}
                    className="w-20 !text-right"
                  />
                ) : (
                  <div
                    onClick={() => {
                      setEditingField("quantity");
                      onEditItem?.(record.id);
                    }}
                    className={`${
                      onEditItem && !disabled
                        ? "cursor-pointer hover:text-blue-600"
                        : ""
                    }`}
                  >
                    {record.quantity}
                  </div>
                ),
            },
            {
              title: "Khối lượng",
              dataIndex: "product_weight",
              key: "product_weight",
              render: (weight: number) => (
                <div className="text-center">
                  {weight ? `${weight} kg` : "N/A"}
                </div>
              ),
            },
            {
              title: "Đơn giá",
              dataIndex: "price",
              key: "price",
              render: (price, record: OrderItem) =>
                formatCurrency(order.is_chain ? record.product_chain_price || price : price),
            },
            {
              title: "Chiết khấu",
              dataIndex: "chiet_khau_amount",
              key: "chiet_khau_amount",
              render: (_, record: OrderItem) =>
                editingItem === record.id ? (
                  <InputNumber
                    min={0}
                    value={Math.round(record.chiet_khau_amount || 0)}
                    onChange={(value) =>
                      handleChietKhauChange(record, Number(value) || 0)
                    }
                    onBlur={() => {
                      setEditingField(null);
                      onEditItem?.(null);
                    }}
                    autoFocus={editingField === "chiet_khau_amount"}
                    disabled={disabled}
                    className="w-24 !text-right"
                  />
                ) : (
                  <div
                    onClick={() => {
                      setEditingField("chiet_khau_amount");
                      onEditItem?.(record.id);
                    }}
                    className={`${
                      onEditItem && !disabled
                        ? "cursor-pointer hover:text-blue-600"
                        : ""
                    }`}
                  >
                    {formatCurrency(record.chiet_khau_amount || 0)}
                  </div>
                ),
            },
            {
              title: "Thành tiền",
              dataIndex: "total_price",
              key: "total_price",
              render: (_, record: OrderItem) => (
                <div className="flex justify-between items-center">
                  <div className="flex-1">
                    {editingItem === record.id ? (
                      <InputNumber
                        min={0}
                        value={Math.round(record.total_price || 0)}
                        onChange={(value) =>
                          handleTotalPriceChange(record, Number(value) || 0)
                        }
                        onBlur={() => {
                          setEditingField(null);
                          onEditItem?.(null);
                        }}
                        autoFocus={editingField === "total_price"}
                        disabled={disabled}
                        className="w-24 !text-right"
                        formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                      />
                    ) : (
                      <div
                        onClick={() => {
                          setEditingField("total_price");
                          onEditItem?.(record.id);
                        }}
                        className={`text-right ${
                          onEditItem && !disabled
                            ? "cursor-pointer hover:text-blue-600"
                            : ""
                        }`}
                      >
                        {formatCurrency(record.total_price)}
                      </div>
                    )}
                  </div>
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveItem(record.id)}
                    disabled={disabled}
                  />
                </div>
              ),
            },
          ]}
          summary={() => (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tạm tính:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"} `}
                >
                  {formatCurrency(subtotal)}
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tổng chiết khấu:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  {formatCurrency(totalDiscount)}
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tổng khối lượng sản phẩm:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  {formatWeight(totalWeight)}
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Phí giao hàng:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  <InputNumber
                    min={0}
                    value={Math.round(shippingFee)}
                    onChange={(value) => {
                      if (!disabled) {
                        setShippingFee(Math.round(Number(value)) || 0);
                      }
                    }}
                    className="w-32 !text-right"
                    disabled={disabled}
                  />
                </Table.Summary.Cell>
              </Table.Summary.Row>

              {/* Voucher Section */}
              {order.promotions && order.promotions.length > 0 && (
                <>
                  <Table.Summary.Row className="bg-blue-50">
                    <Table.Summary.Cell
                      index={0}
                      colSpan={isMobile ? 1 : 6}
                      className={`${
                        isMobile ? "text-left" : "text-right"
                      } text-blue-600 font-medium`}
                    >
                      Voucher đã áp dụng:
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      colSpan={isMobile ? 5 : 1}
                      index={1}
                      className={`${isMobile ? "text-left" : "text-right"}`}
                    >
                      <div className="text-blue-600 font-medium">
                        {order.promotions.length} voucher
                      </div>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                  {order.promotions.map((orderPromotion) => (
                    <Table.Summary.Row
                      key={orderPromotion.id}
                      className="bg-blue-25"
                    >
                      <Table.Summary.Cell
                        index={0}
                        colSpan={isMobile ? 1 : 6}
                        className={`${
                          isMobile ? "text-left" : "text-right"
                        } text-sm`}
                      >
                        <div className="flex items-center gap-2 justify-end">
                          {orderPromotion.promotion.image_url && (
                            <img
                              src={orderPromotion.promotion.image_url}
                              alt={orderPromotion.promotion.code}
                              className="w-6 h-6 object-cover rounded"
                            />
                          )}
                          <div className="text-blue-600">
                            {orderPromotion.promotion.code}
                          </div>
                        </div>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        colSpan={isMobile ? 6 : 1}
                        index={1}
                        className={`${
                          isMobile ? "text-left" : "text-right"
                        } text-sm`}
                      >
                        <div className="text-green-600 font-medium">
                          {`-${formatCurrency(
                            Number(orderPromotion.discount_amount)
                          )}`}
                        </div>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  ))}
                </>
              )}

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Giảm giá:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  <InputNumber
                    min={0}
                    value={Math.round(discount)}
                    onChange={(value) => {
                      if (!disabled) {
                        setDiscount(Math.round(Number(value)) || 0);
                      }
                    }}
                    className="w-32 !text-right"
                    disabled={disabled}
                  />
                </Table.Summary.Cell>
              </Table.Summary.Row>

              {/* Show rank discount info if applicable */}
              {(() => {
                const customerRank = order.user.rank || "normal";
                const rankInfo = CustomerService.getRankInfo(customerRank);

                // Note: With the new logic, rank discount is calculated on discounted amount
                // For existing orders, we can't determine the exact breakdown, so we'll
                // estimate based on the assumption that rank discount is applied last
                const estimatedCustomDiscount = Math.max(
                  0,
                  discount -
                    CustomerService.calculateRankDiscount(
                      subtotal,
                      customerRank
                    )
                );
                const discountedAmount = subtotal - estimatedCustomDiscount;
                const potentialRankDiscount =
                  CustomerService.calculateRankDiscount(
                    discountedAmount,
                    customerRank
                  );

                const hasRankDiscount =
                  customerRank !== "normal" &&
                  discount > 0 &&
                  potentialRankDiscount > 0 &&
                  order.sales_admin !== null;

                if (hasRankDiscount) {
                  return (
                    <Table.Summary.Row className="bg-green-50">
                      <Table.Summary.Cell
                        index={0}
                        colSpan={isMobile ? 1 : 4}
                        className={`${
                          isMobile ? "text-left" : "text-right"
                        } text-green-600`}
                      >
                        ↳ Giảm giá hạng {rankInfo.label} ({rankInfo.discount}%):
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        colSpan={isMobile ? 4 : 1}
                        index={1}
                        className={`${
                          isMobile ? "text-left" : "text-right"
                        } text-green-600`}
                      >
                        -{formatCurrency(potentialRankDiscount)}
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  );
                }
                return null;
              })()}

              <Table.Summary.Row>
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Thuế:
                </Table.Summary.Cell>
                <Table.Summary.Cell colSpan={isMobile ? 6 : 1} index={1}>
                  <div
                    className={`flex items-center gap-2 ${
                      isMobile ? "justify-start" : "justify-end"
                    }`}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      value={tax * 100}
                      onChange={(value) => {
                        if (!disabled) {
                          setTax((Number(value) || 0) / 100);
                        }
                      }}
                      className="w-30 !text-right"
                      disabled={disabled}
                    />
                    <span>%</span>
                  </div>
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row className="bg-gray-50 font-medium">
                <Table.Summary.Cell
                  index={0}
                  colSpan={isMobile ? 1 : 6}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  Tổng cộng:
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  colSpan={isMobile ? 6 : 1}
                  index={1}
                  className={`${isMobile ? "text-left" : "text-right"}`}
                >
                  {formatCurrency(finalTotal)}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </>
          )}
        />
      </div>
    </div>
  );
}
