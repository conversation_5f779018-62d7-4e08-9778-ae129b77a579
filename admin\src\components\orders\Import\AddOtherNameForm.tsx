// src/components/orders/Import/AddOtherNameForm.tsx
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { Product } from "@/types/product";

interface AddOtherNameFormProps {
  ocrProductName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const AddOtherNameForm: React.FC<AddOtherNameFormProps> = ({
  ocrProductName,
  onSuccess,
  onCancel,
}) => {
  const [productCode, setProductCode] = useState('');
  const [otherName, setOtherName] = useState(ocrProductName);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  const handleSubmit = async () => {
    if (!productCode.trim()) {
      showToast('Vui lòng nhập mã sản phẩm', 'error');
      return;
    }

    if (!otherName.trim()) {
      showToast('Vui lòng nhập tên khác cho sản phẩm', 'error');
      return;
    }

    setIsLoading(true);
    try {
      // First, find the product by code
      const searchResponse = await apiCall<{ results: Product[] }>(
        'GET',
        `${endpoints.products.list}?search=${encodeURIComponent(productCode)}&page_size=10`
      );

      // Find exact match by code
      const product = searchResponse.results?.find(p => p.code === productCode.trim());
      
      if (!product) {
        showToast(`Không tìm thấy sản phẩm với mã "${productCode}"`, 'error');
        return;
      }

      // Get current product details to ensure we have the latest other_name array
      const productResponse = await apiCall<Product>(
        'GET',
        endpoints.products.detail(product.id)
      );

      // Check if other name already exists
      const currentOtherNames = productResponse.other_name || [];
      if (currentOtherNames.includes(otherName.trim())) {
        showToast('Tên khác này đã tồn tại cho sản phẩm', 'warning');
        return;
      }

      // Add the new other name
      const updatedOtherNames = [...currentOtherNames, otherName.trim()];
      
      await apiCall(
        'PATCH',
        endpoints.products.update(product.id),
        { other_name: updatedOtherNames }
      );

      showToast(
        `Đã thêm tên khác "${otherName.trim()}" cho sản phẩm "${product.name}" (${product.code})`, 
        'success'
      );
      
      onSuccess();
    } catch (error: any) {
      console.error('Error adding other name:', error);
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.error || 
                          'Không thể thêm tên khác cho sản phẩm';
      showToast(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setProductCode('');
    setOtherName(ocrProductName);
    onCancel();
  };

  return (
    <div className="space-y-3 mt-3 p-4 bg-gray-50 rounded-lg border">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">
          Thêm tên khác cho sản phẩm
        </h4>
        <Button
          onClick={handleCancel}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
      
      <div className="space-y-2">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Mã sản phẩm <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={productCode}
            onChange={(e) => setProductCode(e.target.value.toUpperCase())}
            placeholder="Ví dụ: SP001, ABC123..."
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Tên khác <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={otherName}
            onChange={(e) => setOtherName(e.target.value)}
            placeholder="Tên khác cho sản phẩm..."
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="flex gap-2 pt-2">
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !productCode.trim() || !otherName.trim()}
          size="sm"
          className="flex-1"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Đang xử lý...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              Thêm tên khác
            </>
          )}
        </Button>
        <Button
          onClick={handleCancel}
          variant="outline"
          size="sm"
          disabled={isLoading}
        >
          Hủy
        </Button>
      </div>
    </div>
  );
};
