import { Card, Button, Input, Checkbox } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

interface OrderItemProps {
  item: {
    product_name: string;
    variant_name?: string;
    price: number;
    quantity: number;
    total_price: number;
    unit?: string;
    chiet_khau_amount?: number;
    isTotalPriceAdjustable?: boolean;
  };
  index: number;
  isShowroomEcomm?: boolean;
  onRemove: (index: number) => void;
  onQuantityChange: (index: number, value: number) => void;
  onUnitChange: (index: number, value: string) => void;
  onChietKhauChange?: (index: number, value: number) => void;
  onPriceAdjustmentToggle?: (index: number, enabled: boolean) => void;
  onTotalPriceChange?: (index: number, value: number) => void;
}

export function OrderItem({
  item,
  index,
  isShowroomEcomm = false,
  onRemove,
  onQuantityChange,
  onChietKhauChange,
  onPriceAdjustmentToggle,
  onTotalPriceChange,
}: OrderItemProps) {
  const calculateTotalPrice = () => {
    if (isShowroomEcomm) return 0;
    const baseTotal = item.price * item.quantity;
    const discount = item.chiet_khau_amount || 0;
    return Math.max(0, baseTotal - discount);
  };

  return (
    <Card className="mb-4">
      <div className="flex justify-between items-start mb-4">
        <div>
          <div className="font-medium">{item.product_name}</div>
          {item.variant_name && (
            <div className="text-sm text-gray-500">
              Biến thể: {item.variant_name}
            </div>
          )}
          <div className="text-sm">
            Giá: {isShowroomEcomm ? "0" : Math.round(item.price).toLocaleString()}{" "}
            VND
          </div>
        </div>
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => onRemove(index)}
        >
          Xóa
        </Button>
      </div>

      <div className="grid grid-rows-3 gap-4 items-end">
        <div className="flex flex-row justify-between items-center gap-4 ">
          <div className="text-sm font-medium whitespace-nowrap">Số lượng</div>
          <Input
            type="number"
            min={1}
            value={item.quantity}
            onChange={(e) =>
              onQuantityChange(index, parseInt(e.target.value) || 1)
            }
            className="w-32"
          />
        </div>

        <div>
          <div className="text-sm font-medium mb-1">Chiết khấu</div>
          <div className="flex gap-2 items-center">
            <Input
              type="text"
              min={0}
              value={item.chiet_khau_amount?.toLocaleString() || "0"}
              disabled={isShowroomEcomm}
              onChange={(e) => {
                const value = e.target.value.replace(/[^\d]/g, "");
                onChietKhauChange?.(index, parseInt(value) || 0);
              }}
              className="w-full"
              addonAfter="VND"
              placeholder="Nhập số tiền chiết khấu"
            />
          </div>
        </div>

        <div>
          <div className="text-sm font-medium mb-1">Tổng tiền</div>
          <div className="flex gap-2 items-center">
            <Input
              type="text"
              value={isShowroomEcomm ? "0" : (item.isTotalPriceAdjustable ? item.total_price.toLocaleString() : calculateTotalPrice().toLocaleString())}
              disabled={isShowroomEcomm || !item.isTotalPriceAdjustable}
              onChange={(e) => {
                if (item.isTotalPriceAdjustable && onTotalPriceChange) {
                  const value = e.target.value.replace(/[^\d]/g, "");
                  onTotalPriceChange(index, parseInt(value) || 0);
                }
              }}
              className="w-full"
              addonAfter="VND"
            />
          </div>
          {!isShowroomEcomm && (
            <div className="flex justify-end mt-2">
              <Checkbox
                checked={item.isTotalPriceAdjustable || false}
                onChange={(e) => onPriceAdjustmentToggle?.(index, e.target.checked)}
              >
                Điều chỉnh tổng tiền
              </Checkbox>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
