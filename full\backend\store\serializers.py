from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from .models import Category, Product, Order, OrderItem, Cart, CartItem, ProductImage, ProductVariant, VariantImage, Promotion, PromotionConstraint, CartPromotion, Banner, FavoriteProduct

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']
        read_only_fields = ['id']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'password', 'password2']

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        return attrs

    def create(self, validated_data):
        validated_data.pop('password2')
        user = User.objects.create(
            username=validated_data['username'],
            email=validated_data['email'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )
        user.set_password(validated_data['password'])
        user.save()
        return user

class CategorySerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'parent', 'image_url']
    
    def get_image_url(self, obj):
        if obj.image:
            return self.context['request'].build_absolute_uri(obj.image.url)
        return None

class CategoryTreeSerializer(serializers.ModelSerializer):
    """
    Recursive serializer for categories with children.
    This serializer will include all children categories as a nested structure.
    """
    image_url = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'image_url', 'children']
    
    def get_image_url(self, obj):
        if obj.image:
            return self.context['request'].build_absolute_uri(obj.image.url)
        return None
    
    def get_children(self, obj):
        """
        Recursively get all children of this category
        """
        children = Category.objects.filter(parent=obj)
        serializer = CategoryTreeSerializer(children, many=True, context=self.context)
        return serializer.data

class ProductImageSerializer(serializers.ModelSerializer):
    """Serializer for ProductImage model"""
    class Meta:
        model = ProductImage
        fields = ['id', 'product', 'image', 'alt_text', 'is_primary', 'created_at']
        read_only_fields = ['id', 'created_at']

class VariantImageSerializer(serializers.ModelSerializer):
    """Serializer for VariantImage model"""
    class Meta:
        model = VariantImage
        fields = ['id', 'variant', 'image', 'alt_text', 'is_primary', 'created_at']
        read_only_fields = ['id', 'created_at']

class ProductVariantSerializer(serializers.ModelSerializer):
    """Serializer for ProductVariant model"""
    images = VariantImageSerializer(many=True, read_only=True)
    main_image = serializers.ImageField(read_only=True)
    unit = serializers.CharField(allow_null=True, required=False)
    
    class Meta:
        model = ProductVariant
        fields = ['id', 'product', 'name', 'sku', 'price', 'discount_price',
                  'stock', 'unit', 'is_active', 'max_quantities_in_cart', 'created_at', 'updated_at', 'images', 'main_image']
        read_only_fields = ['id', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    image_url = serializers.SerializerMethodField()
    main_image = serializers.ImageField(read_only=True)
    
    class Meta:
        model = Product
        fields = ['id', 'name', 'description', 'price', 'chain_price', 'discount_price',
                 'category', 'category_name', 'stock', 'image', 'image_url',
                 'is_featured', 'is_active', 'max_quantities_in_cart', 'created_at', 'updated_at',
                 'images', 'variants', 'unit', 'code', 'main_image',
                 'specifications', 'weight']
        read_only_fields = ['id', 'created_at', 'updated_at']
        
    def get_image_url(self, obj):
        if obj.main_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.main_image.url)
            # Fallback to relative URL if no request is available
            return obj.main_image.url
        return None

class OrderItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True, allow_null=True)
    total_price = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True, source='get_total_price'
    )
    
    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'product_name', 'variant', 'variant_name', 'quantity', 'price', 'total_price']
        read_only_fields = ['id']

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Order
        fields = ['id', 'user', 'status', 'status_display', 'total_price', 
                 'shipping_address', 'phone_number', 'email', 
                 'created_at', 'updated_at', 'items']
        read_only_fields = ['id', 'created_at', 'updated_at']

# Serializer for creating orders
class OrderCreateSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True)
    
    class Meta:
        model = Order
        fields = ['shipping_address', 'phone_number', 'email', 'items']
    
    def create(self, validated_data):
        items_data = validated_data.pop('items')
        
        # Calculate total price
        total_price = 0
        for item_data in items_data:
            product = item_data['product']
            variant = item_data.get('variant')
            quantity = item_data['quantity']
            
            # Determine price based on variant or product
            if variant:
                if variant.discount_price:
                    price = variant.discount_price
                elif variant.price:
                    price = variant.price
                elif product.discount_price:
                    price = product.discount_price
                else:
                    price = product.price
            else:
                price = product.discount_price or product.price
                
            # Set the price in the item data
            item_data['price'] = price
            
            # Add to total
            total_price += price * quantity
        
        # Create order
        order = Order.objects.create(
            user=self.context['request'].user,
            total_price=total_price,
            **validated_data
        )
        
        # Create order items
        for item_data in items_data:
            OrderItem.objects.create(order=order, **item_data)
            
            # Update product stock
            product = item_data['product']
            variant = item_data.get('variant')
            quantity = item_data['quantity']
            
            if variant:
                # Update variant stock
                variant.stock -= quantity
                variant.save()
            else:
                # Update product stock
                product.stock -= quantity
                product.save()
            
        return order

class CartItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True, allow_null=True)
    product_price = serializers.SerializerMethodField()
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_name', 'variant', 'variant_name', 'product_price', 'quantity', 'total_price']
        read_only_fields = ['id']
    
    def get_product_price(self, obj):
        if obj.variant:
            return obj.variant.price
        return obj.product.price

class PromotionConstraintSerializer(serializers.ModelSerializer):
    """Serializer for PromotionConstraint model"""
    constraint_type_display = serializers.CharField(source='get_constraint_type_display', read_only=True)
    
    class Meta:
        model = PromotionConstraint
        fields = ['id', 'promotion', 'constraint_type', 'constraint_type_display', 'value']
        read_only_fields = ['id']

class PromotionSerializer(serializers.ModelSerializer):
    """Serializer for Promotion model"""
    constraints = PromotionConstraintSerializer(many=True, read_only=True)
    
    class Meta:
        model = Promotion
        fields = ['id', 'code', 'description', 'is_active', 'value', 'is_percentage', 
                 'created_at', 'updated_at', 'constraints']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CartPromotionSerializer(serializers.ModelSerializer):
    """Serializer for CartPromotion model"""
    promotion = PromotionSerializer(read_only=True)
    code = serializers.CharField(write_only=True)
    
    class Meta:
        model = CartPromotion
        fields = ['id', 'promotion', 'applied_at', 'code']
        read_only_fields = ['id', 'applied_at', 'promotion']
    
    def validate_code(self, value):
        try:
            promotion = Promotion.objects.get(code=value, is_active=True)
        except Promotion.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive promotion code.")
        return value
    
    def create(self, validated_data):
        cart = self.context['cart']
        code = validated_data.pop('code')
        promotion = Promotion.objects.get(code=code, is_active=True)
        
        # Check if this promotion is already applied to the cart
        if CartPromotion.objects.filter(cart=cart, promotion=promotion).exists():
            raise serializers.ValidationError({"code": "This promotion code is already applied to your cart."})
        
        # Check if the promotion constraints are satisfied
        if not promotion.check_constraints(cart):
            raise serializers.ValidationError({"code": "Your cart does not qualify for this promotion."})
        
        return CartPromotion.objects.create(cart=cart, promotion=promotion)

# Update CartSerializer to include promotions
class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    applied_promotions = CartPromotionSerializer(many=True, read_only=True)
    discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_price_after_discount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = Cart
        fields = ['id', 'items', 'total_price', 'applied_promotions', 
                 'discount_amount', 'total_price_after_discount', 
                 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CartItemAddSerializer(serializers.ModelSerializer):
    variant = serializers.PrimaryKeyRelatedField(
        queryset=ProductVariant.objects.filter(is_active=True),
        required=False,
        allow_null=True
    )
    
    class Meta:
        model = CartItem
        fields = ['product', 'variant', 'quantity']
    
    def validate(self, attrs):
        product = attrs['product']
        variant = attrs.get('variant')
        quantity = attrs['quantity']
        
        # Validate that variant belongs to product
        if variant and variant.product != product:
            raise serializers.ValidationError({"variant": "This variant does not belong to the selected product."})
        
        # Check stock availability
        if variant:
            if quantity > variant.stock:
                raise serializers.ValidationError({"quantity": f"Not enough stock available. Only {variant.stock} units left."})
        else:
            if quantity > product.stock:
                raise serializers.ValidationError({"quantity": f"Not enough stock available. Only {product.stock} units left."})
        
        return attrs
    
    def create(self, validated_data):
        cart = self.context['cart']
        product = validated_data['product']
        variant = validated_data.get('variant')
        quantity = validated_data['quantity']
        
        # Check if the item already exists in the cart
        try:
            cart_item = CartItem.objects.get(cart=cart, product=product, variant=variant)
            cart_item.quantity += quantity
            cart_item.save()
        except CartItem.DoesNotExist:
            cart_item = CartItem.objects.create(cart=cart, **validated_data)
            
        return cart_item

class BannerSerializer(serializers.ModelSerializer):
    """Serializer for Banner model"""
    image_url = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Banner
        fields = ['id', 'image', 'image_url', 'link', 'status', 'status_display', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def get_image_url(self, obj):
        if obj.image:
            return self.context['request'].build_absolute_uri(obj.image.url)
        return None

class FavoriteProductSerializer(serializers.ModelSerializer):
    """
    Serializer for the FavoriteProduct model.
    """
    product_details = serializers.SerializerMethodField()
    
    class Meta:
        model = FavoriteProduct
        fields = ['id', 'product', 'product_details', 'created_at']
        read_only_fields = ['created_at']
    
    def get_product_details(self, obj):
        """
        Provide detailed product information.
        """
        from .serializers import ProductSerializer
        product = obj.product
        serializer = ProductSerializer(product, context=self.context)
        return serializer.data
    
    def create(self, validated_data):
        """
        Create a new favorite product.
        Ensure the user field is set to the current user.
        """
        user = self.context['request'].user
        validated_data['user'] = user
        
        # Check if the favorite already exists
        existing = FavoriteProduct.objects.filter(
            user=user, 
            product=validated_data['product']
        ).first()
        
        if existing:
            return existing
        
        return super().create(validated_data)

