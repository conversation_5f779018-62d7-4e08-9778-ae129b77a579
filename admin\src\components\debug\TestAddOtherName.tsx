// src/components/debug/TestAddOtherName.tsx
import React, { useState } from "react";
import { AddOtherNameForm } from "../orders/Import/AddOtherNameForm";

export const TestAddOtherName: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  const handleSuccess = () => {
    console.log('TestAddOtherName: Success callback called');
    setShowForm(false);
  };

  const handleCancel = () => {
    console.log('TestAddOtherName: Cancel callback called');
    setShowForm(false);
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Test AddOtherNameForm</h2>
      
      {!showForm ? (
        <button
          onClick={() => setShowForm(true)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Show AddOtherNameForm
        </button>
      ) : (
        <div className="max-w-md">
          <AddOtherNameForm
            ocrProductName="Test Product Name"
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      )}
      
      <div className="mt-4 p-4 bg-gray-100 rounded">
        <h3 className="font-bold">Debug Instructions:</h3>
        <ol className="list-decimal list-inside mt-2 space-y-1">
          <li>Click "Show AddOtherNameForm"</li>
          <li>Enter a valid product code (e.g., existing product code from your database)</li>
          <li>Enter an other name</li>
          <li>Click "Thêm tên khác"</li>
          <li>Check browser console for debug logs</li>
        </ol>
      </div>
    </div>
  );
};
