// src/components/OrderImport/OrderImportActions.tsx
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs } from "antd";
import { FileText, Image } from "lucide-react";

interface OrderImportActionsProps {
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearData: () => void;
  onSaveData: () => void;
  isSaving: boolean;
  hasData: boolean;
  // OCR-related props
  ocrSection?: React.ReactNode;
  productMatchingSection?: React.ReactNode;
  hasOCRData?: boolean;
}

export const OrderImportActions: React.FC<OrderImportActionsProps> = ({
  onFileChange,
  onClearData,
  onSaveData,
  isSaving,
  hasData,
  ocrSection,
  productMatchingSection,
  hasOCRData = false,
}) => {
  const [activeTab, setActiveTab] = useState<string>("file");

  const tabItems = [
    {
      key: "file",
      label: (
        <span className="flex items-center gap-2">
          <FileText className="w-4 h-4" />
          Import từ File (CSV/Excel)
        </span>
      ),
      children: (
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button asChild className="cursor-pointer">
              <label>
                Chọn File (CSV/Excel)
                <input
                  type="file"
                  className="hidden"
                  accept=".csv,.xlsx,.xls"
                  onChange={onFileChange}
                />
              </label>
            </Button>
            <Button onClick={onClearData} variant="outline" disabled={!hasData}>
              Xoá dữ liệu
            </Button>
            <Button
              onClick={onSaveData}
              disabled={!hasData || isSaving}
              variant="secondary"
            >
              {isSaving ? "Đang lưu..." : "Lưu thông tin"}
            </Button>
          </div>
        </div>
      ),
    },
    {
      key: "ocr",
      label: (
        <span className="flex items-center gap-2">
          <Image className="w-4 h-4" />
          Import từ Hình ảnh (OCR)
        </span>
      ),
      children: (
        <div className="space-y-4">
          {ocrSection}
          {productMatchingSection}
          {hasOCRData && (
            <div className="flex items-center gap-4">
              <Button onClick={onClearData} variant="outline">
                Xoá dữ liệu OCR
              </Button>
              <Button
                onClick={onSaveData}
                disabled={isSaving}
                variant="secondary"
              >
                {isSaving ? "Đang lưu..." : "Tạo đơn hàng từ OCR"}
              </Button>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </CardContent>
    </Card>
  );
};
