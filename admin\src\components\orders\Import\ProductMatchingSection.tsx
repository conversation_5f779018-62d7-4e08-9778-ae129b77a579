// src/components/orders/Import/ProductMatchingSection.tsx
import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "antd";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Search, 
  CheckCircle, 
  AlertTriangle, 
  ExternalLink,
  Plus,
  X
} from "lucide-react";
import { useToast } from "@/context/toast-hooks";
import { apiCall } from "@/lib/api";

interface OCRProduct {
  product: string;
  quantity: number;
}

interface Product {
  id: number;
  name: string;
  other_name: string[];
  code: string;
  price: number;
}

interface MatchedProduct {
  ocrProduct: OCRProduct;
  matchedProduct: Product | null;
  confidence: 'high' | 'medium' | 'low' | 'none';
}

interface ProductMatchingSectionProps {
  ocrProducts: OCRProduct[];
  onMatchedProducts: (matchedProducts: MatchedProduct[]) => void;
}

export const ProductMatchingSection: React.FC<ProductMatchingSectionProps> = ({
  ocrProducts,
  onMatchedProducts,
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [matchedProducts, setMatchedProducts] = useState<MatchedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddOtherName, setShowAddOtherName] = useState<number | null>(null);
  const [newOtherName, setNewOtherName] = useState('');
  const { showToast } = useToast();

  // Load all products on component mount
  useEffect(() => {
    loadProducts();
  }, []);

  // Process OCR products when they change
  useEffect(() => {
    if (ocrProducts.length > 0) {
      processProductMatching();
    }
  }, [ocrProducts, products]);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall<{ results: Product[] }>('GET', '/api/products/');
      setProducts(response.results || []);
    } catch (error) {
      console.error('Error loading products:', error);
      showToast('Không thể tải danh sách sản phẩm', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const processProductMatching = () => {
    const matched: MatchedProduct[] = ocrProducts.map(ocrProduct => {
      const match = findBestMatch(ocrProduct.product);
      return {
        ocrProduct,
        matchedProduct: match.product,
        confidence: match.confidence
      };
    });
    
    setMatchedProducts(matched);
    onMatchedProducts(matched);
  };

  const findBestMatch = (productName: string): { product: Product | null; confidence: 'high' | 'medium' | 'low' | 'none' } => {
    const normalizedName = productName.toLowerCase().trim();
    
    // High confidence: exact name match
    let exactMatch = products.find(p => 
      p.name.toLowerCase() === normalizedName ||
      p.other_name.some(name => name.toLowerCase() === normalizedName)
    );
    if (exactMatch) {
      return { product: exactMatch, confidence: 'high' };
    }

    // Medium confidence: contains match
    let containsMatch = products.find(p => 
      p.name.toLowerCase().includes(normalizedName) ||
      normalizedName.includes(p.name.toLowerCase()) ||
      p.other_name.some(name => 
        name.toLowerCase().includes(normalizedName) ||
        normalizedName.includes(name.toLowerCase())
      )
    );
    if (containsMatch) {
      return { product: containsMatch, confidence: 'medium' };
    }

    // Low confidence: partial word match
    const nameWords = normalizedName.split(/\s+/);
    let partialMatch = products.find(p => {
      const productWords = p.name.toLowerCase().split(/\s+/);
      const otherWords = p.other_name.flatMap(name => name.toLowerCase().split(/\s+/));
      const allWords = [...productWords, ...otherWords];
      
      return nameWords.some(word => 
        allWords.some(productWord => 
          productWord.includes(word) || word.includes(productWord)
        )
      );
    });
    if (partialMatch) {
      return { product: partialMatch, confidence: 'low' };
    }

    return { product: null, confidence: 'none' };
  };

  const handleAddOtherName = async (productId: number) => {
    if (!newOtherName.trim()) return;

    try {
      const product = products.find(p => p.id === productId);
      if (!product) return;

      const updatedOtherNames = [...product.other_name, newOtherName.trim()];
      
      await apiCall('PATCH', `/api/products/${productId}/`, {
        other_name: updatedOtherNames
      });

      // Update local state
      setProducts(prev => prev.map(p => 
        p.id === productId 
          ? { ...p, other_name: updatedOtherNames }
          : p
      ));

      setNewOtherName('');
      setShowAddOtherName(null);
      showToast('Đã thêm tên khác cho sản phẩm', 'success');
      
      // Re-process matching
      processProductMatching();
    } catch (error) {
      console.error('Error adding other name:', error);
      showToast('Không thể thêm tên khác cho sản phẩm', 'error');
    }
  };


  const getConfidenceText = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'Khớp chính xác';
      case 'medium': return 'Khớp tương đối';
      case 'low': return 'Khớp một phần';
      case 'none': return 'Không tìm thấy';
      default: return 'Không xác định';
    }
  };

  if (ocrProducts.length === 0) {
    return null;
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Kết quả tìm kiếm sản phẩm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Đang tải danh sách sản phẩm...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {matchedProducts.map((match, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-lg">{match.ocrProduct.product}</span>
                    <Badge 
                      color={match.confidence === 'high' ? 'green' : match.confidence === 'medium' ? 'orange' : match.confidence === 'low' ? 'yellow' : 'red'}
                    >
                      {getConfidenceText(match.confidence)}
                    </Badge>
                    <span className="text-blue-600 font-semibold">x{match.ocrProduct.quantity}</span>
                  </div>
                </div>

                {match.matchedProduct ? (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{match.matchedProduct.name}</p>
                        <p className="text-sm text-gray-600">Mã: {match.matchedProduct.code}</p>
                        <p className="text-sm text-gray-600">Giá: {match.matchedProduct.price.toLocaleString()}đ</p>
                        {match.matchedProduct.other_name.length > 0 && (
                          <p className="text-sm text-gray-600">
                            Tên khác: {match.matchedProduct.other_name.join(', ')}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/admin/store/product/${match.matchedProduct!.id}/change/`, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        Xem
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Không tìm thấy sản phẩm phù hợp. 
                      <Button
                        variant="link"
                        size="sm"
                        className="p-0 h-auto ml-1"
                        onClick={() => setShowAddOtherName(index)}
                      >
                        Thêm tên khác cho sản phẩm?
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                {showAddOtherName === index && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 mb-2">
                      Thêm "{match.ocrProduct.product}" làm tên khác cho sản phẩm nào?
                    </p>
                    <div className="flex gap-2">
                      <select
                        value=""
                        onChange={(e) => {
                          const productId = parseInt(e.target.value);
                          if (productId) {
                            setNewOtherName(match.ocrProduct.product);
                            handleAddOtherName(productId);
                          }
                        }}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Chọn sản phẩm...</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name} ({product.code})
                          </option>
                        ))}
                      </select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAddOtherName(null)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
