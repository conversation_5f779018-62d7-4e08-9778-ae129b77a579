// src/components/orders/Import/ProductMatchingSection.tsx
import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "antd";
import { Search, Plus, Check, X, AlertTriangle } from "lucide-react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { OCRProduct, MatchedProduct, Product } from "@/types/product";

interface ProductMatchingSectionProps {
  ocrProducts: OCRProduct[];
  onMatchedProducts: (matchedProducts: MatchedProduct[]) => void;
}

export const ProductMatchingSection: React.FC<ProductMatchingSectionProps> = ({
  ocrProducts,
  onMatchedProducts,
}) => {
  const [matchedProducts, setMatchedProducts] = useState<MatchedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddOtherName, setShowAddOtherName] = useState<number | null>(null);
  const [newOtherName, setNewOtherName] = useState('');
  const [productIdInput, setProductIdInput] = useState('');
  const { showToast } = useToast();

  // Process OCR products when they change
  useEffect(() => {
    if (ocrProducts.length > 0) {
      processProductMatching();
    }
  }, [ocrProducts]);

  const searchProducts = async (searchTerm: string): Promise<Product[]> => {
    try {
      const response = await apiCall<{ results: Product[] }>(
        'GET',
        `${endpoints.products.list}?search=${encodeURIComponent(searchTerm)}&page_size=10`
      );
      return response.results || [];
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  };

  const processProductMatching = async () => {
    setIsLoading(true);
    try {
      const matched: MatchedProduct[] = [];

      for (const ocrProduct of ocrProducts) {
        const match = await findBestMatch(ocrProduct.product);
        matched.push({
          ocrProduct,
          matchedProduct: match.product,
          confidence: match.confidence
        });
      }

      setMatchedProducts(matched);
      onMatchedProducts(matched);
    } catch (error) {
      console.error('Error processing product matching:', error);
      showToast('Có lỗi xảy ra khi tìm kiếm sản phẩm', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const findBestMatch = async (ocrProductName: string): Promise<{ product: Product | null; confidence: 'high' | 'medium' | 'low' | 'none' }> => {
    const normalizedOcrName = normalizeProductName(ocrProductName);

    // First, search using the API with the original OCR product name
    const searchResults = await searchProducts(ocrProductName);

    let bestMatch: Product | null = null;
    let bestScore = 0;
    let confidence: 'high' | 'medium' | 'low' | 'none' = 'none';

    // If we have search results, process them
    if (searchResults.length > 0) {
      for (const product of searchResults) {
        // Check exact match with product name
        const normalizedProductName = normalizeProductName(product.name);
        if (normalizedProductName === normalizedOcrName) {
          return { product, confidence: 'high' };
        }

        // Check other names for exact match
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const normalizedOtherName = normalizeProductName(otherName);
            if (normalizedOtherName === normalizedOcrName) {
              return { product, confidence: 'high' };
            }
          }
        }

        // Calculate similarity score with product name
        const score = calculateSimilarity(normalizedOcrName, normalizedProductName);
        if (score > bestScore) {
          bestScore = score;
          bestMatch = product;
        }

        // Check similarity with other names
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const otherScore = calculateSimilarity(normalizedOcrName, normalizeProductName(otherName));
            if (otherScore > bestScore) {
              bestScore = otherScore;
              bestMatch = product;
            }
          }
        }
      }
    }

    // If no good match found in search results, try searching with normalized name
    if (bestScore < 0.6 && normalizedOcrName !== ocrProductName) {
      const fallbackResults = await searchProducts(normalizedOcrName);

      for (const product of fallbackResults) {
        const normalizedProductName = normalizeProductName(product.name);
        const score = calculateSimilarity(normalizedOcrName, normalizedProductName);

        if (score > bestScore) {
          bestScore = score;
          bestMatch = product;
        }

        // Check other names
        if (product.other_name && Array.isArray(product.other_name)) {
          for (const otherName of product.other_name) {
            const otherScore = calculateSimilarity(normalizedOcrName, normalizeProductName(otherName));
            if (otherScore > bestScore) {
              bestScore = otherScore;
              bestMatch = product;
            }
          }
        }
      }
    }

    // Determine confidence based on score
    if (bestScore >= 0.8) {
      confidence = 'high';
    } else if (bestScore >= 0.6) {
      confidence = 'medium';
    } else if (bestScore >= 0.4) {
      confidence = 'low';
    }

    return { product: bestMatch, confidence };
  };

  const normalizeProductName = (name: string): string => {
    return name.toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
  };

  const calculateSimilarity = (str1: string, str2: string): number => {
    // Simple Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  };

  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  const handleAddOtherName = async (productId: number) => {
    if (!newOtherName.trim()) {
      showToast('Vui lòng nhập tên khác cho sản phẩm', 'error');
      return;
    }

    try {
      // Get the current product details
      const productResponse = await apiCall<Product>(
        'GET',
        endpoints.products.detail(productId)
      );

      const updatedOtherNames = [...(productResponse.other_name || []), newOtherName.trim()];

      await apiCall(
        'PATCH',
        endpoints.products.update(productId),
        { other_name: updatedOtherNames }
      );

      setNewOtherName('');
      setProductIdInput('');
      setShowAddOtherName(null);
      showToast(`Đã thêm tên khác "${newOtherName.trim()}" cho sản phẩm ${productResponse.name}`, 'success');

      // Re-process matching after adding other name
      processProductMatching();
    } catch (error) {
      console.error('Error adding other name:', error);
      showToast('Không thể thêm tên khác cho sản phẩm. Vui lòng kiểm tra ID sản phẩm.', 'error');
    }
  };

  const getConfidenceBadgeColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'default';
    }
  };

  const getConfidenceText = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'Khớp cao';
      case 'medium': return 'Khớp trung bình';
      case 'low': return 'Khớp thấp';
      default: return 'Không tìm thấy';
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="w-5 h-5" />
          Kết quả tìm kiếm sản phẩm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Đang tải danh sách sản phẩm...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {matchedProducts.map((match, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <span className="font-medium text-lg">{match.ocrProduct.product}</span>
                    <Badge
                      color={getConfidenceBadgeColor(match.confidence)}
                      text={getConfidenceText(match.confidence)}
                    />
                    <span className="text-sm text-gray-500">
                      Số lượng: {match.ocrProduct.quantity}
                    </span>
                  </div>
                </div>

                {match.matchedProduct ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <Check className="w-4 h-4" />
                    <span>Tìm thấy: {match.matchedProduct.name}</span>
                    {match.matchedProduct.code && (
                      <span className="text-sm text-gray-500">({match.matchedProduct.code})</span>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-red-600">
                      <X className="w-4 h-4" />
                      <span>Không tìm thấy sản phẩm phù hợp</span>
                    </div>

                    {showAddOtherName === index ? (
                      <div className="space-y-2 mt-2 p-3 bg-gray-50 rounded-md">
                        <div className="text-sm font-medium text-gray-700">
                          Thêm tên khác cho sản phẩm
                        </div>
                        <div className="flex gap-2">
                          <input
                            type="number"
                            value={productIdInput}
                            onChange={(e) => setProductIdInput(e.target.value)}
                            placeholder="Nhập ID sản phẩm..."
                            className="w-32 px-3 py-2 border rounded-md text-sm"
                          />
                          <input
                            type="text"
                            value={newOtherName}
                            onChange={(e) => setNewOtherName(e.target.value)}
                            placeholder={`Tên khác: "${match.ocrProduct.product}"`}
                            className="flex-1 px-3 py-2 border rounded-md text-sm"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => {
                              const productId = parseInt(productIdInput);
                              if (productId && newOtherName.trim()) {
                                handleAddOtherName(productId);
                              } else {
                                showToast('Vui lòng nhập ID sản phẩm và tên khác', 'error');
                              }
                            }}
                            size="sm"
                            className="flex-1"
                          >
                            <Check className="w-4 h-4 mr-2" />
                            Thêm tên khác
                          </Button>
                          <Button
                            onClick={() => {
                              setShowAddOtherName(null);
                              setNewOtherName('');
                              setProductIdInput('');
                            }}
                            variant="outline"
                            size="sm"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <Button
                        onClick={() => setShowAddOtherName(index)}
                        variant="outline"
                        size="sm"
                        className="mt-2"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Thêm tên khác cho sản phẩm
                      </Button>
                    )}
                  </div>
                )}

                {match.confidence === 'low' && match.matchedProduct && (
                  <div className="flex items-center gap-2 text-yellow-600 mt-2">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm">Độ khớp thấp, vui lòng kiểm tra lại</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};