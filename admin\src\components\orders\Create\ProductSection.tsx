import React from "react";
import {
  Button,
  Empty,
  Card,
  Divider,
  Statistic,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { OrderItem as OrderItemType } from "../../../types/order";

// Extend OrderItem type to include unit property
interface ExtendedOrderItem extends OrderItemType {
  unit?: string;
  chiet_khau_amount?: number;
  isTotalPriceAdjustable?: boolean;
}

interface ProductSectionProps {
  items: ExtendedOrderItem[];
  totalPrice: number;
  isShowroom?: boolean;
  onAddProduct: () => void;
  onRemoveItem: (index: number) => void;
  onQuantityChange: (index: number, quantity: number) => void;
  onUnitChange: (index: number, unit: string) => void;
  onChietKhauChange: (index: number, chietKhauAmount: number) => void;
  onPriceAdjustmentToggle?: (index: number, enabled: boolean) => void;
  onTotalPriceChange?: (index: number, value: number) => void;
}

const ProductSection: React.FC<ProductSectionProps> = ({
  items,
  totalPrice,
  isShowroom = false,
  onAddProduct,
  onRemoveItem,
  onQuantityChange,
  onUnitChange,
  onChietKhauChange,
  onPriceAdjustmentToggle,
  onTotalPriceChange,
}) => {
  return (
    <div>
      <Card className="mb-4">
        <div className="flex justify-between items-center mb-4">
          <span className="font-bold text-xl">Sản phẩm</span>
          <Button type="primary" icon={<PlusOutlined />} onClick={onAddProduct}>
            Thêm sản phẩm
          </Button>
        </div>
        {items.length === 0 ? (
          <Empty
            description="Chưa có sản phẩm nào"
            className="border-2 border-dashed rounded py-8 my-4"
          />
        ) : (
          <div className="space-y-4">
            {items.map((item, index) => (
              <OrderItem
                key={index}
                item={item}
                index={index}
                isShowroomEcomm={isShowroom}
                onRemove={onRemoveItem}
                onQuantityChange={onQuantityChange}
                onUnitChange={onUnitChange}
                onChietKhauChange={onChietKhauChange}
                onPriceAdjustmentToggle={onPriceAdjustmentToggle}
                onTotalPriceChange={onTotalPriceChange}
              />
            ))}

            <Divider />
            <div className="flex justify-end">
              <Statistic
                title="Tổng cộng (tạm tính)"
                value={Math.round(totalPrice)}
                suffix="VND"
                groupSeparator=","
                className="text-right"
              />
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

// Import the existing OrderItem component
import { OrderItem } from "./OrderItem";

export default ProductSection;
