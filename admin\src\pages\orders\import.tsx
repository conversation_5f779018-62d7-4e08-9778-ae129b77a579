// src/pages/OrderImportPage.tsx
import React from "react";
import { useOrderImport } from "@/hooks/useOrderImport";
import { useOCRImport } from "@/hooks/useOCRImport";
import { OrderImportInstructions } from "@/components/orders/Import/OrderImportInstructions";
import { OrderImportActions } from "@/components/orders/Import/OrderImportActions";
import { OrderImportTable } from "@/components/orders/Import/OrderImportTable";
import { ImageUploadSection } from "@/components/orders/Import/ImageUploadSection";
import { ProductMatchingSection } from "@/components/orders/Import/ProductMatchingSection";

const OrderImportPage = () => {
  const {
    data,
    isSaving,
    processedRows,
    hasErrors,
    handleFileChange,
    handleSaveOrders,
    clearData,
    downloadErrorRows,
  } = useOrderImport();

  const {
    ocrProducts,
    imageUpload,
    productMatching,
    handleOCRResult,
    handleMatchedProducts,
    clearOCRData,
    hasOCRData,
  } = useOCRImport();

  const handleClearData = () => {
    clearData();
    clearOCRData();
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-4">Import Đơn Hàng</h1>

      <OrderImportInstructions />

      <OrderImportActions
        onFileChange={handleFileChange}
        onClearData={handleClearData}
        onSaveData={handleSaveOrders}
        isSaving={isSaving}
        hasData={data.length > 0}
        hasOCRData={ocrProducts.length > 0}
        ocrSection={
          <ImageUploadSection
            onOCRResult={handleOCRResult}
            onClearOCR={clearOCRData}
          />
        }
        productMatchingSection={
          ocrProducts.length > 0 && (
            <ProductMatchingSection
              ocrProducts={ocrProducts}
              onMatchedProducts={handleMatchedProducts}
            />
          )
        }
      />

      <OrderImportTable
        data={data}
        processedRows={processedRows}
        hasErrors={hasErrors}
        onDownloadErrors={downloadErrorRows}
      />
    </div>
  );
};

export default OrderImportPage;
