// src/pages/OrderImportPage.tsx
import React, { useState } from "react";
import { useOrderImport } from "@/hooks/useOrderImport";
import { OrderImportInstructions } from "@/components/orders/Import/OrderImportInstructions";
import { OrderImportActions } from "@/components/orders/Import/OrderImportActions";
import { OrderImportTable } from "@/components/orders/Import/OrderImportTable";
import { ImageUploadSection } from "@/components/orders/Import/ImageUploadSection";
import { ProductMatchingSection } from "@/components/orders/Import/ProductMatchingSection";
import { OCRProduct, MatchedProduct } from "@/types/product";

const OrderImportPage = () => {
  const {
    data,
    isSaving,
    processedRows,
    hasErrors,
    handleFileChange,
    handleSaveOrders,
    clearData,
    downloadErrorRows,
  } = useOrderImport();

  // OCR-related state
  const [ocrProducts, setOcrProducts] = useState<OCRProduct[]>([]);
  const [matchedProducts, setMatchedProducts] = useState<MatchedProduct[]>([]);

  const handleOCRResult = (products: OCRProduct[]) => {
    setOcrProducts(products);
  };

  const handleMatchedProducts = (matched: MatchedProduct[]) => {
    setMatchedProducts(matched);
  };

  const handleClearOCR = () => {
    setOcrProducts([]);
    setMatchedProducts([]);
  };

  const handleClearData = () => {
    clearData();
    handleClearOCR();
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-4">Import Đơn Hàng</h1>

      <OrderImportInstructions />

      <OrderImportActions
        onFileChange={handleFileChange}
        onClearData={handleClearData}
        onSaveData={handleSaveOrders}
        isSaving={isSaving}
        hasData={data.length > 0}
        hasOCRData={ocrProducts.length > 0}
        ocrSection={
          <ImageUploadSection
            onOCRResult={handleOCRResult}
            onClearOCR={handleClearOCR}
          />
        }
        productMatchingSection={
          ocrProducts.length > 0 && (
            <ProductMatchingSection
              ocrProducts={ocrProducts}
              onMatchedProducts={handleMatchedProducts}
            />
          )
        }
      />

      <OrderImportTable
        data={data}
        processedRows={processedRows}
        hasErrors={hasErrors}
        onDownloadErrors={downloadErrorRows}
      />
    </div>
  );
};

export default OrderImportPage;
