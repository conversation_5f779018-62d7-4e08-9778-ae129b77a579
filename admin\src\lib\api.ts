import axios from "axios";
import { ProductRevenueResponse } from "@/types/report";

const baseURL = import.meta.env.VITE_API_URL || "http://localhost:8000/api";

export const api = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Only clear token if not on login page to prevent clearing during login attempts
      const isLoginPage = window.location.pathname === "/login";
      if (!isLoginPage) {
        localStorage.removeItem("token");
        // Don't redirect, let the components handle navigation
      }
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const endpoints = {
  reports: {
    list: '/reports',
    listAll: '/reports/all_orders_revenue/',
    productRevenue: '/reports/products/',
    allProductRevenue: '/reports/all_products/',
    compareCustomerRevenue: '/reports/customer_revenue_comparison/',
    topCustomer: '/reports/top_customers/',
    customerReport: '/reports/user_stats/',
    compareRevenue: '/reports/revenue_comparison/',
    compareRevenueAll: '/reports/all_orders_revenue_comparison/',
    deliveryRevenue: '/reports/delivery_revenue/',
  },
  auth: {
    login: "/token/",
    refresh: "/token/refresh/",
    me: "/users/me/",
  },
  orders: {
    list: "/orders",
    create: "/orders/",
    detail: (id: number) => `/orders/${id}`,
    update: (id: number) => `/orders/${id}/`,
    updateStatus: (id: number) => `/orders/${id}/update_status/`,
    statusCounts: "/orders/status_counts",
    confirm: (id: number) => `/orders/${id}/confirm/`,
    reset_time: "/orders/reset_timers/",
    ocr: "/order-ocr/",
  },
  products: {
    list: "/products",
    detail: (id: number) => `/products/${id}`,
    create: "/products/",
    update: (id: number) => `/products/${id}/`,
    delete: (id: number) => `/products/${id}/`,
    categories: "/categories",
    images: (id: number) => `/products/${id}/images`,
    variants: (id: number) => `/products/${id}/variants`,
    addImages: "/product-images/",
  },
  inventory: {
    logs: "/inventory-logs/",
    triggerUpdate: "/scrape_inventory/?run=manual",
    stats: "/inventory-logs/stats/",
  },
  categories: {
    list: "/categories",
    detail: (id: number) => `/categories/${id}`,
    create: "/categories",
    update: (id: number) => `/categories/${id}`,
    delete: (id: number) => `/categories/${id}`,
  },
  customers: {
    list: "/users",
    detail: (id: number) => `/users/${id}`,
    create: "/users",
    update: (id: number) => `/users/${id}/`,
    delete: (id: number) => `/users/${id}/`,
    stats: (id: number) => `/users/${id}/stats`,
    orders: (id: number) => `/users/${id}/orders`,
    createCustomer: "/users/create-customer/",
    notes: (id: number) => `/users/${id}/notes/`,
  },
  staff: {
    list: "/users", // Use the same base endpoint as customers, backend filters roles
    detail: (id: number) => `/users/${id}`, // Assuming staff detail uses the same endpoint
    create: "/users/create-staff/",
    update: (id: number) => `/users/${id}/`, // Same as customers update endpoint
    updateUser: (id: number) => `/users/${id}/update_user/`, // Specific endpoint for updating user info
  },
} as const;

export interface ProductRevenueParams {
  dateFrom?: string;
  dateTo?: string;
  category?: number;
  page?: number;
  page_size?: number;
  sales_admin?: string; // Can be a single ID or comma-separated IDs
  no_page?: boolean;
  search?: string;
  search_by?: 'code' | 'name'; // Field to search by
}

export async function getProductRevenue(params?: ProductRevenueParams, includeAllOrders: boolean = false) {
  const queryParams = new URLSearchParams();
  if (params?.dateFrom) queryParams.append('dateFrom', params.dateFrom);
  if (params?.dateTo) queryParams.append('dateTo', params.dateTo);
  if (params?.category) queryParams.append('category', params.category.toString());
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
  if (params?.sales_admin) queryParams.append('sales_admin', params.sales_admin);
  if (params?.search) queryParams.append('search', params.search);
  if (params?.search_by) queryParams.append('search_by', params.search_by);
  if (params?.no_page) queryParams.append('no_page', 'true');

  // Choose the appropriate endpoint based on whether to include all orders
  const endpoint = includeAllOrders
    ? endpoints.reports.allProductRevenue
    : endpoints.reports.productRevenue;

  const url = `${endpoint}?${queryParams.toString()}`;
  return apiCall<ProductRevenueResponse>('GET', url);
}

// Helper function to make API calls
export async function apiCall<T>(
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE",
  url: string,
  data?: unknown,
  options?: { isFormData?: boolean }
): Promise<T> {
  try {
    const config: any = {
      method,
      url,
      data,
    };

    if (options?.isFormData) {
      config.headers = {
        ...config.headers,
        "Content-Type": "multipart/form-data",
      };
    }

    const response = await api.request<T>(config);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      // Preserve the original error response
      throw error;
    }
    throw error;
  }
}
