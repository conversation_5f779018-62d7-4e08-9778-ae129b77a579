from django.db import models
from safedelete.models import SafeDeleteModel
from safedelete.models import SOFT_DELETE, SOFT_DELETE_CASCADE
import uuid
import random
import string

class Category(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='children')
    image = models.ImageField(upload_to='categories/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.parent.name} / {self.name}" if self.parent else self.name



    class Meta:
        verbose_name_plural = "Categories"

class Product(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE
    name = models.Char<PERSON>ield(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=15, decimal_places=2)
    chain_price = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    weight = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    unit = models.CharField(max_length=50, null=True, blank=True)
    specifications = models.TextField(null=True, blank=True)
    discount_price = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    category = models.ForeignKey(Category, related_name='products', on_delete=models.SET_NULL, null=True, blank=True)
    stock = models.PositiveIntegerField(default=0)
    image = models.ImageField(upload_to='products/', blank=True, null=True)  # Keep for backward compatibility
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    code = models.CharField(max_length=50, blank=True, unique=True)  # Unique code for the product - Mã sản phẩm
    max_quantities_in_cart = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum quantity of this product that can be added to cart. Leave empty for no limit.")
    last_stock_update = models.DateTimeField(null=True, blank=True, help_text="Thời gian cập nhật stock gần nhất từ Arito")
    is_chain = models.BooleanField(default=False, help_text="Indicates whether this product is for chain stores")
    other_name = models.JSONField(default=list, blank=True, help_text="Mảng các tên khác của sản phẩm")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def delete(self, force_policy=None, **kwargs):
        """Override delete to add random suffix to code to avoid unique constraint issues"""
        if not self.deleted:
            # Add random suffix to code before soft delete
            random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            original_code = self.code
            self.code = f"{original_code}_DEL_{random_suffix}"
            self.save()

        # Call parent delete method for soft delete
        return super().delete(force_policy=force_policy, **kwargs)

    def undelete(self, **kwargs):
        """Override undelete to restore original code"""
        if self.deleted and '_DEL_' in self.code:
            # Restore original code by removing the suffix
            self.code = self.code.split('_DEL_')[0]
            self.save()

        # Call parent undelete method
        return super().undelete(**kwargs)

    @property
    def main_image(self):
        """Return the main product image"""
        # First try to get primary image
        primary_image = self.images.filter(is_primary=True).first()
        if primary_image:
            return primary_image.image

        # If no primary image, get the first image
        first_image = self.images.first()
        if first_image:
            return first_image.image

        # Fall back to legacy image field if no images exist
        return self.image

class ProductImage(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE
    """Model for storing multiple images for a product"""
    product = models.ForeignKey(Product, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='products/')
    alt_text = models.CharField(max_length=255, blank=True)
    is_primary = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Image for {self.product.name}"

    class Meta:
        ordering = ['sort_order', 'created_at']

class ProductVariant(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE
    """Model for product variants (different sizes, colors, etc.)"""
    product = models.ForeignKey(Product, related_name='variants', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    sku = models.CharField(max_length=100, blank=True)
    price = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    discount_price = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    stock = models.PositiveIntegerField(default=0)
    unit = models.CharField(max_length=50, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    max_quantities_in_cart = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum quantity of this variant that can be added to cart. Leave empty to inherit from product or no limit.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.product.name} - {self.name}"



    @property
    def effective_price(self):
        """Return the variant price or fall back to product price if not set"""
        if self.price is not None:
            return self.price
        return self.product.price

    @property
    def effective_discount_price(self):
        """Return the variant discount price or fall back to product discount price if not set"""
        if self.discount_price is not None:
            return self.discount_price
        return self.product.discount_price

    @property
    def main_image(self):
        """Return the main variant image or fall back to product image"""
        primary_image = self.images.filter(is_primary=True).first()
        if primary_image:
            return primary_image.image

        # If no primary image, get the first image
        first_image = self.images.first()
        if first_image:
            return first_image.image

        # Fall back to product image if no variant images
        return self.product.main_image

class VariantImage(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE
    """Model for storing images specific to a product variant"""
    variant = models.ForeignKey(ProductVariant, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='products/variants/')
    alt_text = models.CharField(max_length=255, blank=True)
    is_primary = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Image for {self.variant}"

    class Meta:
        ordering = ['sort_order', 'created_at']
