// src/hooks/useOCRImport.ts
import { useState } from "react";
import { OCRProduct, MatchedProduct } from "@/types/product";
import { useImageUpload } from "./useImageUpload";
import { useProductMatching } from "./useProductMatching";

export const useOCRImport = () => {
  const [ocrProducts, setOcrProducts] = useState<OCRProduct[]>([]);
  const [matchedProducts, setMatchedProducts] = useState<MatchedProduct[]>([]);

  // Image upload hook
  const imageUpload = useImageUpload();

  // Product matching hook
  const productMatching = useProductMatching(ocrProducts);

  // Handle OCR result from image processing
  const handleOCRResult = async () => {
    try {
      const products = await imageUpload.processOCR();
      setOcrProducts(products);
      return products;
    } catch (error) {
      console.error('OCR processing failed:', error);
      return [];
    }
  };

  // Handle matched products from product matching
  const handleMatchedProducts = (matched: MatchedProduct[]) => {
    setMatchedProducts(matched);
  };

  // Clear all OCR data
  const clearOCRData = () => {
    setOcrProducts([]);
    setMatchedProducts([]);
    imageUpload.clearAll();
  };

  // Reprocess matching (useful after adding other names)
  const reprocessMatching = () => {
    productMatching.reprocessMatching();
  };

  return {
    // OCR Products State
    ocrProducts,
    matchedProducts: productMatching.matchedProducts,
    
    // Image Upload
    imageUpload: {
      selectedFile: imageUpload.selectedFile,
      previewUrl: imageUpload.previewUrl,
      isProcessing: imageUpload.isProcessing,
      ocrResult: imageUpload.ocrResult,
      error: imageUpload.error,
      fileInputRef: imageUpload.fileInputRef,
      handleFileSelect: imageUpload.handleFileSelect,
      handleDrop: imageUpload.handleDrop,
      handleDragOver: imageUpload.handleDragOver,
      removeFile: imageUpload.removeFile,
    },
    
    // Product Matching
    productMatching: {
      isLoading: productMatching.isLoading,
      matchedProducts: productMatching.matchedProducts,
    },
    
    // Actions
    handleOCRResult,
    handleMatchedProducts,
    clearOCRData,
    reprocessMatching,
    
    // Computed
    hasOCRData: ocrProducts.length > 0,
    hasMatchedProducts: productMatching.matchedProducts.length > 0,
  };
};
