// src/hooks/useOCRToOrder.ts
import { useNavigate } from "react-router-dom";
import { useToast } from "@/context/toast-hooks";
import { MatchedProduct } from "@/types/product";
import { OrderItem } from "@/types/order";

export const useOCRToOrder = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();

  const convertMatchedProductsToOrderItems = (matchedProducts: MatchedProduct[]): OrderItem[] => {
    const orderItems: OrderItem[] = [];
    
    matchedProducts.forEach((match, index) => {
      // Only include products with high confidence matches
      if (match.confidence === 'high' && match.matchedProduct) {
        const product = match.matchedProduct;
        const ocrProduct = match.ocrProduct;
        
        const orderItem: OrderItem = {
          id: index + 1, // Temporary ID for new items
          product: product.id,
          product_name: product.name,
          product_code: product.code,
          quantity: ocrProduct.quantity,
          price: product.price,
          total_price: product.price * ocrProduct.quantity,
          product_weight: product.weight,
          unit: product.unit,
        };
        
        orderItems.push(orderItem);
      }
    });
    
    return orderItems;
  };

  const createOrderFromOCR = (matchedProducts: MatchedProduct[]) => {
    console.log('createOrderFromOCR: Starting conversion', matchedProducts);
    
    // Filter only high confidence matches
    const highConfidenceMatches = matchedProducts.filter(
      match => match.confidence === 'high' && match.matchedProduct
    );
    
    if (highConfidenceMatches.length === 0) {
      showToast('Không có sản phẩm nào với độ khớp cao để tạo đơn hàng', 'info');
      return;
    }
    
    // Convert to order items
    const orderItems = convertMatchedProductsToOrderItems(highConfidenceMatches);
    console.log('createOrderFromOCR: Converted order items', orderItems);
    
    // Store in sessionStorage to pass to create order page
    sessionStorage.setItem('ocrOrderItems', JSON.stringify(orderItems));
    
    // Show success message
    showToast(
      `Đã chuyển ${orderItems.length} sản phẩm với độ khớp cao sang trang tạo đơn hàng`, 
      'success'
    );
    
    // Navigate to create order page
    navigate('/orders/create');
  };

  const getOCROrderItems = (): OrderItem[] => {
    try {
      const storedItems = sessionStorage.getItem('ocrOrderItems');
      if (storedItems) {
        const items = JSON.parse(storedItems) as OrderItem[];
        // Clear after retrieving to avoid stale data
        sessionStorage.removeItem('ocrOrderItems');
        return items;
      }
    } catch (error) {
      console.error('Error retrieving OCR order items:', error);
    }
    return [];
  };

  const clearOCROrderItems = () => {
    sessionStorage.removeItem('ocrOrderItems');
  };

  return {
    createOrderFromOCR,
    getOCROrderItems,
    clearOCROrderItems,
    convertMatchedProductsToOrderItems,
  };
};
