"""
Admin actions for the store app.
This file contains all the admin actions for the store app.
"""
import io
from django import forms
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill
from django.http import HttpResponse
# Removed duplicate import of ValidationError
from django.contrib import admin
from typing import Optional

from django.contrib import messages
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.shortcuts import render
from django.http import HttpResponseRedirect
from decimal import Decimal, InvalidOperation
import pandas as pd
from django.core.exceptions import ValidationError
from ..models import ProductImage, VariantImage, Product, Category
from ..services.email_service import EmailService


class PriceUpdateFileForm(forms.Form):
    """
    Form for uploading price update file
    """
    file = forms.FileField(
        label='Chọn file Excel/CSV',
        help_text='File phải có 2 cột: Mã hàng và G<PERSON>á bán'
    )

    def clean_file(self):
        file = self.cleaned_data['file']
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            raise forms.ValidationError('Chỉ hỗ trợ file Excel (.xlsx, .xls) hoặc CSV')
        return file


class InfoUpdateFileForm(forms.Form):
    """
    Form for uploading product info update file
    """
    file = forms.FileField(
        label='Chọn file Excel/CSV',
        help_text='File phải có các cột: Mã hàng, Mô tả, Mô tả chi tiết, Cân nặng, Đơn vị tính, Sản phẩm chuỗi, Đơn giá chuỗi'
    )

    def clean_file(self):
        file = self.cleaned_data['file']
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            raise forms.ValidationError('Chỉ hỗ trợ file Excel (.xlsx, .xls) hoặc CSV')
        return file


class ProductImportFileForm(forms.Form):
    """
    Form for importing new products from Excel/CSV file
    """
    file = forms.FileField(
        label='Chọn file Excel/CSV',
        help_text='File phải có các cột: Mã hàng, Tên sản phẩm, Mô tả, Giá bán, Danh mục, ...'
    )

    def clean_file(self):
        file = self.cleaned_data['file']
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            raise forms.ValidationError('Chỉ hỗ trợ file Excel (.xlsx, .xls) hoặc CSV')
        return file


def make_active(modeladmin, request, queryset):
    queryset.update(is_active=True)
make_active.short_description = "Đánh dấu sản phẩm đang hoạt động"

def make_inactive(modeladmin, request, queryset):
    queryset.update(is_active=False)
make_inactive.short_description = "Đánh dấu sản phẩm không hoạt động"

def mark_as_featured(modeladmin, request, queryset):
    queryset.update(is_featured=True)
mark_as_featured.short_description = "Đánh dấu là sản phẩm nổi bật"

def mark_orders_as_processing(modeladmin, request, queryset):
    queryset.update(status='processing')
mark_orders_as_processing.short_description = "Đánh dấu đơn hàng đang xử lý"

def mark_orders_as_shipped(modeladmin, request, queryset):
    queryset.update(status='shipped')
mark_orders_as_shipped.short_description = "Đánh dấu đơn hàng đã gửi"

def mark_as_primary_image(modeladmin, request, queryset):
    # First, unmark all primary images for the related products
    for image in queryset:
        ProductImage.objects.filter(product=image.product, is_primary=True).update(is_primary=False)
    # Then mark the selected images as primary
    queryset.update(is_primary=True)
mark_as_primary_image.short_description = "Đánh dấu là hình ảnh chính"

def mark_as_primary_variant_image(modeladmin, request, queryset):
    # First, unmark all primary images for the related variants
    for image in queryset:
        VariantImage.objects.filter(variant=image.variant, is_primary=True).update(is_primary=False)
    # Then mark the selected images as primary
    queryset.update(is_primary=True)
mark_as_primary_variant_image.short_description = "Đánh dấu là hình ảnh chính"

def make_banner_active(modeladmin, request, queryset):
    queryset.update(status='active')
make_banner_active.short_description = "Đánh dấu banner đang hoạt động"

def make_banner_inactive(modeladmin, request, queryset):
    queryset.update(status='inactive')
make_banner_inactive.short_description = "Đánh dấu banner không hoạt động"

def send_promotional_email(modeladmin, request, queryset):
    """
    Send promotional email for selected products
    """
    try:
        sent_count = EmailService.send_promotional_email(queryset)
        modeladmin.message_user(
            request,
            f"Đã gửi email thành công đến {sent_count} người nhận",
            messages.SUCCESS
        )
    except Exception as e:
        modeladmin.message_user(
            request,
            f"Lỗi khi gửi email: {str(e)}",
            messages.ERROR
        )

send_promotional_email.short_description = "Gửi email khuyến mãi cho sản phẩm đã chọn"

def update_prices(modeladmin, request, queryset):
    if 'apply' in request.POST:
        updated_count = 0
        for item in queryset:
            price_key = f'price_{item.id}'
            if price_key in request.POST:
                try:
                    new_price = Decimal(request.POST[price_key])
                    item.price = new_price
                    item.save()
                    updated_count += 1
                except (ValueError, TypeError):
                    pass

        modeladmin.message_user(request, f'Updated prices for {updated_count} items')
        return HttpResponseRedirect(request.get_full_path())

    context = {
        'items': queryset,
        'title': 'Cập nhật giá cho từng sản phẩm',
        'action': 'update_prices'
    }
    return render(request, 'admin/price_update_form.html', context)

update_prices.short_description = "Cập nhật giá hàng loạt"

def update_prices_by_file(modeladmin, request, queryset):
    """
    Update product prices using an uploaded Excel/CSV file
    """
    form = None

    # Form submission
    # First check if this is a POST request and the file was uploaded
    if request.method == 'POST' and 'file' in request.FILES and 'action' in request.POST:

        form = PriceUpdateFileForm(request.POST, request.FILES)
        if form.is_valid():
            print('Form is valid, processing file')
            file = request.FILES['file']
            try:
                # Read the file with pandas
                if file.name.endswith('.csv'):
                    df = pd.read_csv(file)
                else:
                    df = pd.read_excel(file)

                # Validate columns
                required_columns = ['Mã hàng', 'Giá bán']
                if not all(col in df.columns for col in required_columns):
                    raise ValidationError('File phải có 2 cột: Mã hàng và Giá bán')

                # Update prices
                updated_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        product_code = str(row['Mã hàng'])

                        price_str = str(row['Giá bán']).replace(',', '')  # Handle comma in numbers
                        new_price = Decimal(price_str)

                        if new_price <= 0:
                            errors.append(f'Dòng {index + 2}: Giá phải lớn hơn 0')
                            continue

                        try:

                            product = Product.objects.get(code=product_code)
                            product.price = new_price
                            product.save()
                            updated_count += 1
                        except Product.DoesNotExist:
                            errors.append(f'Không tìm thấy sản phẩm có Mã {product_code}')

                    except ValueError:
                        errors.append(f'Dòng {index + 2}: Mã hàng hoặc giá không hợp lệ')
                    except InvalidOperation:
                        errors.append(f'Dòng {index + 2}: Giá không hợp lệ')

                # Show results
                if updated_count > 0:
                    modeladmin.message_user(
                        request,
                        f'Đã cập nhật giá cho {updated_count} sản phẩm thành công',
                        messages.SUCCESS
                    )
                from django.utils.safestring import mark_safe

                if errors:
                    error_message = '<br>'.join(errors)
                    scrollable_error_html = f'''
                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                            {error_message}
                        </div>
                    '''
                    modeladmin.message_user(
                        request,
                        mark_safe('Lỗi:<br>' + scrollable_error_html),
                        messages.ERROR
                )
                # Redirect to products list page
                return HttpResponseRedirect('./')

            except pd.errors.EmptyDataError:
                modeladmin.message_user(
                    request,
                    'File rỗng hoặc không có dữ liệu',
                    messages.ERROR
                )
            except Exception as e:
                modeladmin.message_user(
                    request,
                    f'Lỗi khi xử lý file: {str(e)}',
                    messages.ERROR
                )

    context = {
        'form': PriceUpdateFileForm(request.POST, request.FILES) if request.method == 'POST' else PriceUpdateFileForm(),
        'title': 'Cập nhật giá bằng file Excel/CSV',
        'action': 'update_prices_by_file',
        'opts': modeladmin.model._meta,
        'queryset': queryset,
        'action_checkbox_name': ACTION_CHECKBOX_NAME,
    }
    return render(request, 'admin/price_update_file_form.html', context)

update_prices_by_file.short_description = "Cập nhật giá bằng file Excel/CSV"

def update_info_by_file(modeladmin, request, queryset):
    """
    Update product information using an uploaded Excel/CSV file
    """
    # Form submission
    if request.method == 'POST' and 'file' in request.FILES and 'action' in request.POST:
        form = InfoUpdateFileForm(request.POST, request.FILES)
        if form.is_valid():
            file = request.FILES['file']
            try:
                # Read the file with pandas
                if file.name.endswith('.csv'):
                    df = pd.read_csv(file)
                else:
                    df = pd.read_excel(file)

                # Validate columns
                required_columns = ['Mã hàng']
                optional_columns = ['Mô tả chi tiết', 'Cân nặng', 'Đơn vị tính', 'Sản phẩm chuỗi', 'Đơn giá chuỗi']

                if not all(col in df.columns for col in required_columns):
                    raise ValidationError('File phải có cột Mã hàng')

                # Update product info
                updated_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        product_code = str(row['Mã hàng'])

                        try:

                            product = Product.objects.get(code=product_code)

                            # Update optional fields only if value exists and is valid

                            # Tên sản phẩm
                            if 'Tên' in df.columns:
                                name = row['Tên']
                                if pd.notna(name) and str(name).strip() != '':
                                    product.name = str(name)

                            # Mô tả
                            if 'Mô tả' in df.columns:
                                desc = row['Mô tả']
                                if pd.notna(desc) and str(desc).strip() != '':
                                    product.description = str(desc)

                            # Mô tả chi tiết
                            if 'Mô tả chi tiết' in df.columns:
                                detail = row['Mô tả chi tiết']
                                if pd.notna(detail) and str(detail).strip() != '':
                                    product.specifications = str(detail)

                            # Cân nặng
                            if 'Cân nặng' in df.columns:
                                weight = row['Cân nặng']
                                if pd.notna(weight):
                                    try:
                                        product.weight = float(weight)
                                    except (ValueError, TypeError):
                                        errors.append(f'Dòng {index + 2}: Cân nặng không hợp lệ')
                                        continue  # Bỏ qua cập nhật nếu cân nặng sai

                            # Đơn vị tính
                            if 'Đơn vị tính' in df.columns:
                                unit = row['Đơn vị tính']
                                if pd.notna(unit) and str(unit).strip() != '':
                                    product.unit = str(unit)

                            # Sản phẩm chuỗi
                            if 'Sản phẩm chuỗi' in df.columns:
                                is_chain = row['Sản phẩm chuỗi']
                                if pd.notna(is_chain):
                                    try:
                                        # Convert to string and check various formats
                                        is_chain_str = str(is_chain).strip().lower()
                                        if is_chain_str in ['1', 'true', 'yes', 'có', 'co', 'chain']:
                                            product.is_chain = True
                                        elif is_chain_str in ['0', 'false', 'no', 'không', 'khong', 'normal']:
                                            product.is_chain = False
                                        else:
                                            # Try to convert to int/float first
                                            try:
                                                numeric_value = float(is_chain_str)
                                                product.is_chain = bool(numeric_value)
                                            except ValueError:
                                                errors.append(f'Dòng {index + 2}: Giá trị "Sản phẩm chuỗi" không hợp lệ (chỉ chấp nhận 1/0, true/false)')
                                                continue
                                    except Exception:
                                        errors.append(f'Dòng {index + 2}: Lỗi xử lý giá trị "Sản phẩm chuỗi"')
                                        continue

                            # Đơn giá chuỗi
                            if 'Đơn giá chuỗi' in df.columns:
                                chain_price_value = row['Đơn giá chuỗi']
                                if pd.notna(chain_price_value):
                                    try:
                                        chain_price_str = str(chain_price_value).replace(',', '')
                                        product.chain_price = Decimal(chain_price_str)
                                    except (ValueError, TypeError, InvalidOperation):
                                        errors.append(f'Dòng {index + 2}: Đơn giá chuỗi không hợp lệ')
                                        continue

                            product.save()
                            updated_count += 1

                        except Product.DoesNotExist:
                            errors.append(f'Không tìm thấy sản phẩm có Mã hàng {product_code}')

                    except ValueError:
                        errors.append(f'Dòng {index + 2}: Mã hàng sản phẩm không hợp lệ')

                # Show results
                if updated_count > 0:
                    modeladmin.message_user(
                        request,
                        f'Đã cập nhật thông tin cho {updated_count} sản phẩm thành công',
                        messages.SUCCESS
                    )

                from django.utils.safestring import mark_safe

                if errors:
                    error_message = '<br>'.join(errors)
                    scrollable_error_html = f'''
                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                            {error_message}
                        </div>
                    '''
                    modeladmin.message_user(
                        request,
                        mark_safe('Lỗi:<br>' + scrollable_error_html),
                        messages.ERROR
                )
                # Redirect to products list page
                return HttpResponseRedirect('./')

            except pd.errors.EmptyDataError:
                modeladmin.message_user(
                    request,
                    'File rỗng hoặc không có dữ liệu',
                    messages.ERROR
                )
            except Exception as e:
                modeladmin.message_user(
                    request,
                    f'Lỗi khi xử lý file: {str(e)}',
                    messages.ERROR
                )

    context = {
        'form': InfoUpdateFileForm(request.POST, request.FILES) if request.method == 'POST' else InfoUpdateFileForm(),
        'title': 'Cập nhật thông tin sản phẩm bằng file Excel/CSV',
        'action': 'update_info_by_file',
        'opts': modeladmin.model._meta,
        'queryset': queryset,
        'action_checkbox_name': ACTION_CHECKBOX_NAME,
    }
    return render(request, 'admin/info_update_file_form.html', context)

update_info_by_file.short_description = "Cập nhật thông tin bằng file Excel/CSV"

def export_products(modeladmin, request, queryset):
    """
    Export selected products to Excel file
    """
    # Create a new workbook and sheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Sản phẩm"

    # Define headers
    headers = ['Mã hàng', 'Tên', 'Khối lượng', 'Đơn vị tính', 'Sản phẩm chuỗi', 'Đơn giá chuỗi', 'Mô tả', 'Mô tả chi tiết' ]
    ws.append(headers)

    # Define styles for deleted products
    deleted_font = Font(color="FF0000", strike=True)  # Red color with strikethrough

    # Add data
    row_num = 2  # Start from row 2 (after headers)
    for product in queryset:
        row = [
            product.code,
            product.name,
            product.weight or '',
            product.unit or '',
            1 if product.is_chain else 0,  # Convert boolean to 1/0
            product.chain_price or '',
            product.description or '',
            product.specifications or '',
        ]
        ws.append(row)

        # Apply styling to deleted products
        if product.deleted:
            for col_num in range(1, len(headers) + 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.font = deleted_font

        row_num += 1

    # Auto-adjust columns width
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=products_export.xlsx'

    # Save workbook to response
    wb.save(response)
    return response

export_products.short_description = "Xuất mã hàng các sản phẩm đã chọn"

def import_products_from_file(modeladmin, request, queryset=None):
    """
    Import new products from an uploaded Excel/CSV file
    This action can be run without selecting any products
    """
    # Form submission
    if request.method == 'POST' and 'file' in request.FILES and 'action' in request.POST:
        form = ProductImportFileForm(request.POST, request.FILES)
        if form.is_valid():
            file = request.FILES['file']
            try:
                # Read the file with pandas
                if file.name.endswith('.csv'):
                    df = pd.read_csv(file)
                else:
                    df = pd.read_excel(file)

                # Validate columns
                required_columns = ['Mã hàng', 'Tên sản phẩm', 'Giá bán']

                if not all(col in df.columns for col in required_columns):
                    raise ValidationError(f'File phải có các cột bắt buộc: {", ".join(required_columns)}')

                # Import products
                created_count = 0
                updated_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        product_code = str(row['Mã hàng']).strip()
                        product_name = str(row['Tên sản phẩm']).strip()

                        # Validate required fields
                        if not product_code:
                            errors.append(f'Dòng {index + 2}: Mã hàng không được để trống')
                            continue

                        if not product_name:
                            errors.append(f'Dòng {index + 2}: Tên sản phẩm không được để trống')
                            continue

                        # Check if product with this code already exists
                        product_exists = Product.objects.filter(code=product_code).exists()

                        if product_exists:
                            errors.append(f'Dòng {index + 2}: Sản phẩm với mã {product_code} đã tồn tại')
                            continue

                        # Get price
                        try:
                            price = Decimal(str(row['Giá bán']).replace(',', '.'))
                            if price < 0:
                                errors.append(f'Dòng {index + 2}: Giá bán không được âm')
                                continue
                        except (InvalidOperation, ValueError, TypeError):
                            errors.append(f'Dòng {index + 2}: Giá bán không hợp lệ')
                            continue

                        # Create new product
                        product = Product(
                            code=product_code,
                            name=product_name,
                            price=price,
                            is_active=True
                        )

                        # Optional fields
                        # Description
                        if 'Mô tả' in df.columns:
                            description = row['Mô tả']
                            if pd.notna(description) and str(description).strip() != '':
                                product.description = str(description)
                            else:
                                product.description = ''  # Default empty description
                        else:
                            product.description = ''

                        # Discount price
                        if 'Giá khuyến mãi' in df.columns:
                            discount_price = row['Giá khuyến mãi']
                            if pd.notna(discount_price):
                                try:
                                    discount_price = Decimal(str(discount_price).replace(',', '.'))
                                    if 0 <= discount_price < price:
                                        product.discount_price = discount_price
                                except (InvalidOperation, ValueError, TypeError):
                                    pass  # Ignore invalid discount price

                        # Stock
                        if 'Tồn kho' in df.columns:
                            stock = row['Tồn kho']
                            if pd.notna(stock):
                                try:
                                    stock = int(stock)
                                    if stock >= 0:
                                        product.stock = stock
                                except (ValueError, TypeError):
                                    pass  # Ignore invalid stock

                        # Category
                        if 'Danh mục' in df.columns:
                            category_name = row['Danh mục']
                            if pd.notna(category_name) and str(category_name).strip() != '':
                                # Try to find category by name
                                try:
                                    category = Category.objects.get(name=str(category_name).strip())
                                    product.category = category
                                except Category.DoesNotExist:
                                    # Category doesn't exist, note in errors but continue
                                    errors.append(f'Dòng {index + 2}: Danh mục "{category_name}" không tồn tại')

                        # Specifications
                        if 'Mô tả chi tiết' in df.columns:
                            specs = row['Mô tả chi tiết']
                            if pd.notna(specs) and str(specs).strip() != '':
                                product.specifications = str(specs)

                        # Weight
                        if 'Cân nặng' in df.columns:
                            weight = row['Cân nặng']
                            if pd.notna(weight):
                                try:
                                    product.weight = float(weight)
                                except (ValueError, TypeError):
                                    pass  # Ignore invalid weight

                        # Unit
                        if 'Đơn vị tính' in df.columns:
                            unit = row['Đơn vị tính']
                            if pd.notna(unit) and str(unit).strip() != '':
                                product.unit = str(unit)

                        # Featured
                        if 'Nổi bật' in df.columns:
                            featured = row['Nổi bật']
                            if pd.notna(featured):
                                if isinstance(featured, bool):
                                    product.is_featured = featured
                                elif str(featured).lower() in ['true', 'yes', '1', 'có', 'co']:
                                    product.is_featured = True

                        # Chain product
                        if 'Sản phẩm chuỗi' in df.columns:
                            is_chain = row['Sản phẩm chuỗi']
                            if pd.notna(is_chain):
                                try:
                                    # Convert to string and check various formats
                                    is_chain_str = str(is_chain).strip().lower()
                                    if is_chain_str in ['1', 'true', 'yes', 'có', 'co', 'chain']:
                                        product.is_chain = True
                                    elif is_chain_str in ['0', 'false', 'no', 'không', 'khong', 'normal']:
                                        product.is_chain = False
                                    else:
                                        # Try to convert to int/float first
                                        try:
                                            numeric_value = float(is_chain_str)
                                            product.is_chain = bool(numeric_value)
                                        except ValueError:
                                            errors.append(f'Dòng {index + 2}: Giá trị "Sản phẩm chuỗi" không hợp lệ (chỉ chấp nhận 1/0, true/false)')
                                            continue
                                except Exception:
                                    errors.append(f'Dòng {index + 2}: Lỗi xử lý giá trị "Sản phẩm chuỗi"')
                                    continue

                        # Save the product
                        product.save()
                        created_count += 1

                    except Exception as e:
                        errors.append(f'Dòng {index + 2}: Lỗi - {str(e)}')

                # Show results
                if created_count > 0:
                    modeladmin.message_user(
                        request,
                        f'Đã thêm thành công {created_count} sản phẩm mới',
                        messages.SUCCESS
                    )

                from django.utils.safestring import mark_safe

                if errors:
                    error_message = '<br>'.join(errors)
                    scrollable_error_html = f'''
                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                            {error_message}
                        </div>
                    '''
                    modeladmin.message_user(
                        request,
                        mark_safe('Lỗi:<br>' + scrollable_error_html),
                        messages.ERROR
                    )

                # Redirect to products list page
                return HttpResponseRedirect('./')

            except pd.errors.EmptyDataError:
                modeladmin.message_user(
                    request,
                    'File rỗng hoặc không có dữ liệu',
                    messages.ERROR
                )
            except Exception as e:
                modeladmin.message_user(
                    request,
                    f'Lỗi khi xử lý file: {str(e)}',
                    messages.ERROR
                )

    context = {
        'form': ProductImportFileForm(request.POST, request.FILES) if request.method == 'POST' else ProductImportFileForm(),
        'title': 'Thêm sản phẩm mới bằng file Excel/CSV',
        'action': 'import_products',
    }

    # Nếu được gọi từ action trong admin, thêm các thông tin cần thiết
    if hasattr(modeladmin, 'model') and queryset is not None:
        context.update({
            'opts': modeladmin.model._meta,
            'queryset': queryset,
            'action_checkbox_name': ACTION_CHECKBOX_NAME,
        })

    return render(request, 'admin/product_import_file_form.html', context)

import_products_from_file.short_description = "Thêm sản phẩm mới bằng file Excel/CSV"


def restore_deleted_products(modeladmin, request, queryset):
    """
    Restore soft-deleted products and restore original codes
    """
    restored_count = 0
    errors = []

    for product in queryset:
        if product.deleted:
            try:
                product.undelete()  # This will also restore the original code
                restored_count += 1
            except Exception as e:
                errors.append(f'Lỗi khôi phục sản phẩm {product.name}: {str(e)}')

    if restored_count > 0:
        modeladmin.message_user(
            request,
            f'Đã khôi phục {restored_count} sản phẩm thành công',
            messages.SUCCESS
        )

    if errors:
        modeladmin.message_user(
            request,
            f'Có lỗi xảy ra: {"; ".join(errors)}',
            messages.ERROR
        )

    if restored_count == 0 and not errors:
        modeladmin.message_user(
            request,
            'Không có sản phẩm nào cần khôi phục',
            messages.WARNING
        )

restore_deleted_products.short_description = "Khôi phục sản phẩm đã xóa"


def update_max_quantities_in_cart(modeladmin, request, queryset):
    """
    Update max_quantities_in_cart for all selected products with the same value
    """
    if 'apply' in request.POST:
        try:
            new_quantity_str = request.POST.get('max_quantity', '').strip()

            # Handle empty string (no limit)
            if not new_quantity_str:
                new_quantity = None
                quantity_display = "không giới hạn"
            else:
                # Validate positive integer
                new_quantity = int(new_quantity_str)
                if new_quantity <= 0:
                    modeladmin.message_user(
                        request,
                        'Số lượng phải lớn hơn 0 hoặc để trống để không giới hạn',
                        messages.ERROR
                    )
                    return HttpResponseRedirect(request.get_full_path())
                quantity_display = str(new_quantity)

            # Update all selected products
            updated_count = queryset.update(max_quantities_in_cart=new_quantity)

            # Show success message
            modeladmin.message_user(
                request,
                f'Đã cập nhật giới hạn giỏ hàng thành "{quantity_display}" cho {updated_count} sản phẩm',
                messages.SUCCESS
            )

        except (ValueError, TypeError):
            modeladmin.message_user(
                request,
                'Số lượng không hợp lệ. Vui lòng nhập số nguyên dương hoặc để trống.',
                messages.ERROR
            )

        return HttpResponseRedirect(request.get_full_path())

    context = {
        'products': queryset,
        'title': 'Cập nhật giới hạn số lượng trong giỏ hàng',
        'action': 'update_max_quantities_in_cart',
        'product_count': queryset.count()
    }
    return render(request, 'admin/max_quantities_update_form.html', context)

update_max_quantities_in_cart.short_description = "Cập nhật giới hạn số lượng trong giỏ hàng"

