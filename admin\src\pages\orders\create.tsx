import { useState } from "react";
import { useNavigate } from "react-router-dom";

import { slugify } from "@/lib/utils";
import { api, endpoints } from "@/lib/api";
import { useAuth } from "@/context/auth-hooks";
import { useToast } from "@/context/toast-hooks";

import { OrderItem as BaseOrderItem, Customer } from "@/types";
import { isApiError } from "@/types/api";
import { CustomerService, OrderService } from "@/services";
import { ProductSelectionDialog } from "@/components/orders";
import { CustomerSearchDialog } from "@/components/customers";
import { formatErrorMessage } from "./utils";

import {
  CreateOrderPaymentMethod,
  CreateOrderNotes,
  ProductSection,
  CustomerInfoForm,
  TransportationInfo,
  DeliveryDateSection,
  RankDiscountSection,
  ShowroomOrderSection,
  TaxSection,
  DiscountSection,
  type ShowroomOrderType,
} from "@/components/orders/Create";

interface OrderItem extends BaseOrderItem {
  unit?: string;
  isTotalPriceAdjustable?: boolean;
}


interface OrderForm {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  notes?: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_unit?: string;
  shipping_fee?: number;
  discount?: number;
  is_showroom: boolean;
  is_e_comm?: boolean;
  delivery_date?: string;
  tax?: number;
  have_tax: boolean;
}

export default function CreateOrderPage() {
  const [notes, setNotes] = useState("");
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { user } = useAuth();
  const [applyRankDiscount, setApplyRankDiscount] = useState(true);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [isCreatingNewCustomer, setIsCreatingNewCustomer] = useState(false);
  const [useTransportation, setUseTransportation] = useState(false);
  const [showroomOrderType, setShowroomOrderType] =
    useState<ShowroomOrderType | null>("normal_order");
  const [transportInfo, setTransportInfo] = useState<{
    name: string;
    address: string;
    phone: string;
    operatingHours: string;
  }>({ name: "", address: "", phone: "", operatingHours: "" });
  const [shippingFee, setShippingFee] = useState(0);
  const [taxEnabled, setTaxEnabled] = useState(false);
  const [taxValue, setTaxValue] = useState(8);
  const [discountEnabled, setDiscountEnabled] = useState(false);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [newCustomerForm, setNewCustomerForm] = useState({
    first_name: "",
    last_name: "",
    phone_number: "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    creator_id: user?.id || 0,
  });

  const createCustomer = async () => {
    // Validate required fields
    if (!newCustomerForm.first_name) {
      throw new Error("Vui lòng nhập tên khách hàng");
    }
    if (!newCustomerForm.phone_number) {
      throw new Error("Vui lòng nhập số điện thoại");
    }
    if (!newCustomerForm.shipping_address) {
      throw new Error("Vui lòng nhập địa chỉ giao hàng");
    }
    if (
      !newCustomerForm.ward ||
      !newCustomerForm.district ||
      !newCustomerForm.city
    ) {
      throw new Error("Vui lòng nhập đầy đủ thông tin phường, quận, thành phố");
    }

    const timestamp = Date.now().toString().slice(-6);
    const email = `${slugify(
      newCustomerForm.first_name
    )}-${timestamp}@placeholder.com`;

    // Create user with the form data
    const response = await api.post(endpoints.customers.createCustomer, {
      ...newCustomerForm,
      last_name: "",
      email,
    });

    const newUser = response.data.user;

    const orderFormData: OrderForm = {
      user: newUser.id,
      phone_number: newCustomerForm.phone_number,
      email: email,
      shipping_address: newCustomerForm.shipping_address,
      ward: newCustomerForm.ward,
      district: newCustomerForm.district,
      city: newCustomerForm.city,
      payment_method: orderForm.payment_method,
      payment_status: orderForm.payment_status,
      company_payment_received: orderForm.company_payment_received,
      shipping_fee: shippingFee,
      is_showroom: orderForm.is_showroom,
      is_e_comm: orderForm.is_e_comm,
      delivery_date: orderForm.delivery_date,
      tax: taxEnabled ? taxValue / 100 : 0,
      have_tax: taxEnabled,
    };

    const newSelectedCustomer = {
      id: newUser.id,
      username: newUser.username,
      first_name: newUser.first_name,
      last_name: newUser.last_name,
      email: newUser.email,
      phone_number: newUser.phone_number,
      is_active: newUser.is_active,
      rank: "normal" as const,
      profile: {
        phone_number: newCustomerForm.phone_number,
        shipping_address: newCustomerForm.shipping_address,
        ward: newCustomerForm.ward,
        district: newCustomerForm.district,
        city: newCustomerForm.city,
        rank: "normal" as const,
      },
    };

    setSelectedCustomer(newSelectedCustomer);
    setOrderForm(orderFormData);
    setIsCreatingNewCustomer(false);

    return { user: newUser, orderForm: orderFormData };
  };
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [items, setItems] = useState<OrderItem[]>([]);

  const handleRemoveCustomer = () => {
    setSelectedCustomer(null);

    setOrderForm({
      phone_number: "",
      email: "",
      shipping_address: "",
      ward: "",
      district: "",
      city: "",
      payment_method: orderForm.payment_method,
      payment_status: orderForm.payment_status,
      company_payment_received: orderForm.company_payment_received,
      shipping_fee: shippingFee,
      is_showroom: orderForm.is_showroom,
      delivery_date: orderForm.delivery_date,
      tax: taxEnabled ? taxValue / 100 : 0,
      have_tax: taxEnabled,
    });
  };
  const [loading, setLoading] = useState(false);
  const [orderForm, setOrderForm] = useState<OrderForm>({
    phone_number: "",
    email: "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    payment_method: "cod" as const,
    payment_status: "unpaid" as const,
    company_payment_received: false,
    shipping_fee: 0,
    discount: 0,
    is_showroom: false,
    is_e_comm: false,
    delivery_date: undefined,
    tax: 0,
    have_tax: false,
  });

  const totalPrice = items.reduce((sum, item) => {
    if (orderForm.is_showroom || orderForm.is_e_comm) return sum;
    
    if (item.isTotalPriceAdjustable) {
      return sum + (item.total_price || 0);
    } else {
      const baseTotal = item.price * item.quantity;
      const discount = item.chiet_khau_amount || 0;
      return sum + Math.max(0, baseTotal - discount);
    }
  }, 0);



  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);

    setOrderForm({
      user: customer.id,
      phone_number: customer.profile.phone_number || "",
      email: customer.email,
      shipping_address: customer.profile.shipping_address || "",
      ward: customer.profile.ward || "",
      district: customer.profile.district || "",
      city: customer.profile.city || "",
      payment_method: orderForm.payment_method,
      payment_status: orderForm.payment_status,
      company_payment_received: orderForm.company_payment_received,
      shipping_fee: shippingFee,
      is_showroom: orderForm.is_showroom,
      is_e_comm: orderForm.is_e_comm,
      delivery_date: orderForm.delivery_date,
      tax: taxEnabled ? taxValue / 100 : 0,
      have_tax: taxEnabled,
    });

    setIsCustomerDialogOpen(false);
  };

  const handleAddItem = (newItems: OrderItem[]) => {
    setItems((prevItems) => {
      const updatedItems = newItems.reduce((acc, item) => {
        const existingItemIndex = acc.findIndex(
          (i) => i.product === item.product && i.variant === item.variant
        );

        if (existingItemIndex >= 0) {
          return acc.map((prevItem, index) => {
            if (index === existingItemIndex) {
              const newQuantity = prevItem.quantity + item.quantity;
              return {
                ...prevItem,
                quantity: newQuantity,
                // total_price sẽ được tính tự động
              };
            }
            return prevItem;
          });
        }
        return [
          ...acc,
          orderForm.is_showroom 
            ? { ...item, chiet_khau_amount: 0, isTotalPriceAdjustable: false } 
            : { 
                ...item,
                chiet_khau_amount: 0,
                isTotalPriceAdjustable: false,
              },
        ];
      }, prevItems);

      return updatedItems;
    });
  };

  const handleRemoveItem = (index: number) => {
    setItems((prevItems) => {
      const updatedItems = prevItems.filter((_, i) => i !== index);
      return updatedItems;
    });
  };

  const handleQuantityChange = (index: number, quantity: number) => {
    setItems((prevItems) => {
      const updatedItems = prevItems.map((item, i) =>
        i === index
          ? {
              ...item,
              quantity,
            }
          : item
      );
      return updatedItems;
    });
  };

  const handlePriceAdjustmentToggle = (index: number, enabled: boolean) => {
    setItems((prevItems) => {
      const updatedItems = prevItems.map((item, i) =>
        i === index
          ? {
              ...item,
              isTotalPriceAdjustable: enabled,
              // Reset total_price to calculated value when disabling
              total_price: enabled ? item.total_price : item.price * item.quantity,
            }
          : item
      );
      return updatedItems;
    });
  };

  const handleTotalPriceChange = (index: number, value: number) => {
    setItems((prevItems) => {
      const updatedItems = prevItems.map((item, i) =>
        i === index
          ? {
              ...item,
              total_price: value,
            }
          : item
      );
      return updatedItems;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (items.length === 0) {
      showToast("Vui lòng thêm ít nhất một sản phẩm", "error");
      return;
    }

    try {
      setLoading(true);

      let orderData = { ...orderForm };

      if (isCreatingNewCustomer) {
        try {
          const { orderForm: newOrderForm } = await createCustomer();
          orderData = newOrderForm;
        } catch (error) {
          console.error("Không thể tạo khách hàng:", error);
          if (error instanceof Error) {
            showToast(error.message, "error");
          } else {
            showToast("Không thể tạo khách hàng", "error");
          }
          return;
        }
      }

      // Calculate custom discount first
      let customDiscount = 0;
      if (discountEnabled) {
        customDiscount = discountAmount;
      }

      // Calculate rank discount on the discounted amount (after custom discount)
      let rankDiscount = 0;
      if (
        applyRankDiscount &&
        selectedCustomer?.rank &&
        selectedCustomer.rank !== "normal" &&
        !orderData.is_showroom &&
        !orderData.is_e_comm
      ) {
        // Apply rank discount on the amount after custom discount
        const discountedAmount = totalPrice - customDiscount;
        rankDiscount = CustomerService.calculateRankDiscount(
          discountedAmount,
          selectedCustomer.rank
        );
      }

      // Prepare parameters for service
      const totalDiscount = rankDiscount + customDiscount;
      const serviceParams = {
        orderData: {
          ...orderData,
          notes,
          discount: totalDiscount,
          tax: taxEnabled ? taxValue / 100 : 0,
          have_tax: taxEnabled,
          is_e_comm: orderForm.is_e_comm,
        },
        items,
        useTransportation,
        transportInfo,
        shippingFee,
        notes,
      };

      // Use service to handle order creation
      const result = await OrderService.handleOrderCreation(
        showroomOrderType,
        serviceParams
      );

      showToast(result.message, "success");
      navigate(`/orders/${result.redirectOrderId}`);
    } catch (error) {
      console.error("Failed to create order:", error);
      if (isApiError(error)) {
        const errorMessage = error.response.data[0] || "Lỗi không xác định";
        // Format error message for better display
        const formattedMessage = formatErrorMessage(errorMessage);
        showToast(`Không thể tạo đơn hàng:\n${formattedMessage}`, "error");
      } else {
        showToast("Không thể tạo đơn hàng. Vui lòng thử lại", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-8xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Tạo đơn hàng mới</h1>
        <button
          onClick={() => navigate("/orders")}
          className="text-gray-600 hover:text-gray-800"
        >
          ← Trở về danh sách đơn hàng
        </button>
      </div>

      <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Customer Information */}
        <CustomerInfoForm
          selectedCustomer={selectedCustomer}
          isCreatingNewCustomer={isCreatingNewCustomer}
          newCustomerForm={newCustomerForm}
          orderForm={orderForm}
          onNewCustomerFormChange={(field, value) => {
            setNewCustomerForm({
              ...newCustomerForm,
              [field]: value,
            });
          }}
          onOrderFormChange={(field, value) => {
            setOrderForm((prev) => ({
              ...prev,
              [field]: value,
            }));
          }}
          onSelectCustomerClick={() => setIsCustomerDialogOpen(true)}
          onRemoveCustomer={handleRemoveCustomer}
          onCreateNewCustomerClick={() => setIsCreatingNewCustomer(true)}
          onCancelCreateNewCustomer={() => setIsCreatingNewCustomer(false)}
        />

        {/* Products */}
        <div>
          <ProductSection
            items={items}
            totalPrice={
              orderForm.is_showroom || orderForm.is_e_comm ? 0 : totalPrice
            }
            isShowroom={orderForm.is_showroom || orderForm.is_e_comm}
            onAddProduct={() => setIsProductDialogOpen(true)}
            onRemoveItem={handleRemoveItem}
            onQuantityChange={(index, quantity) => {
              handleQuantityChange(index, quantity);
            }}

            onUnitChange={(index, unit) => {
              setItems((prevItems) => {
                const newItems = [...prevItems];
                newItems[index] = {
                  ...newItems[index],
                  unit,
                };
                return newItems;
              });
            }}
            onChietKhauChange={(index, chietKhauAmount) => {
              setItems((prevItems) => {
                const newItems = [...prevItems];
                const item = newItems[index];
                
                newItems[index] = {
                  ...item,
                  chiet_khau_amount: chietKhauAmount,
                };
                return newItems;
              });
            }}
            onPriceAdjustmentToggle={handlePriceAdjustmentToggle}
            onTotalPriceChange={handleTotalPriceChange}
          />

          <CreateOrderPaymentMethod
            paymentMethod={orderForm.payment_method}
            paymentStatus={orderForm.payment_status}
            companyPaymentReceived={orderForm.company_payment_received}
            onPaymentMethodChange={(value: "cod" | "cash" | "bank_transfer") =>
              setOrderForm({
                ...orderForm,
                payment_method: value,
              })
            }
            onPaymentStatusChange={(value: "paid" | "unpaid") =>
              setOrderForm({
                ...orderForm,
                payment_status: value,
              })
            }
            onCompanyPaymentReceivedChange={(value: boolean) =>
              setOrderForm({
                ...orderForm,
                company_payment_received: value,
              })
            }
          />

          <ShowroomOrderSection
            isShowroom={orderForm.is_showroom}
            selectedOrderType={showroomOrderType}
            onShowroomChange={(value: boolean, skipItemHandling?: boolean) => {
              setOrderForm((prev) => ({
                ...prev,
                is_showroom: value,
              }));

              // Only handle items if not skipping (for backward compatibility and warehouse option)
              if (!skipItemHandling) {
                OrderService.handleShowroomToggle(
                  value,
                  items,
                  setItems,
                  showToast
                );
              }
            }}
            onECommerceChange={(value: boolean) => {
              setOrderForm((prev) => ({
                ...prev,
                is_e_comm: value,
              }));
            }}
            onShowroomOrderTypeSelect={(type: ShowroomOrderType) => {
              setShowroomOrderType(type);

              // Reset e-commerce state when selecting non-e-commerce options
              if (type !== "e_commerce_order") {
                setOrderForm((prev) => ({
                  ...prev,
                  is_e_comm: false,
                }));
              }

              OrderService.handleShowroomOrderTypeSelection(
                type,
                items,
                setItems,
                showToast
              );
            }}
          />
        </div>

        {/* Notes, Transportation Info, and Delivery Date */}

        <div className="flex flex-col gap-4">
          {/* Rank Discount Section */}
          {selectedCustomer &&
            selectedCustomer.rank &&
            selectedCustomer.rank !== "normal" && (
              <RankDiscountSection
                customerRank={selectedCustomer.rank}
                subtotal={totalPrice}
                shippingFee={shippingFee}
                applyRankDiscount={applyRankDiscount}
                onApplyRankDiscountChange={setApplyRankDiscount}
                existingDiscount={discountEnabled ? discountAmount : 0}
              />
            )}

          <CreateOrderNotes notes={notes} onChange={setNotes} />

          <DeliveryDateSection
            deliveryDate={orderForm.delivery_date}
            onDeliveryDateChange={(date) => {
              setOrderForm((prev) => ({
                ...prev,
                delivery_date: date,
              }));
            }}
          />

          <TransportationInfo
            notes={notes}
            onTransportationChange={setUseTransportation}
            onTransportInfoChange={setTransportInfo}
            transportInfo={transportInfo}
            onShippingFeeChange={(fee) => {
              setShippingFee(fee);
              setOrderForm((prev) => ({
                ...prev,
                shipping_fee: fee,
              }));
            }}
            shippingFee={shippingFee}
          />

          <DiscountSection
            discountEnabled={discountEnabled}
            onDiscountEnabledChange={setDiscountEnabled}
            discountAmount={discountAmount}
            onDiscountAmountChange={setDiscountAmount}
            subtotal={totalPrice}
          />

          <TaxSection
            taxEnabled={taxEnabled}
            onTaxEnabledChange={setTaxEnabled}
            taxValue={taxValue}
            onTaxValueChange={setTaxValue}
          />
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <button
          onClick={handleSubmit}
          disabled={
            loading ||
            items.length === 0 ||
            (!selectedCustomer && !isCreatingNewCustomer)
          }
          className="bg-green-500 text-white px-6 py-2 rounded text-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? "Đang tạo..." : "Tạo đơn hàng"}
        </button>
      </div>

      <ProductSelectionDialog
        isOpen={isProductDialogOpen}
        onClose={() => setIsProductDialogOpen(false)}
        onAdd={handleAddItem}
      />

      <CustomerSearchDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSelect={handleSelectCustomer}
        onCreateNew={() => {
          setIsCreatingNewCustomer(true);
          setIsCustomerDialogOpen(false);
        }}
      />
    </div>
  );
}
