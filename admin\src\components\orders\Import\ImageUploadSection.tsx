// src/components/orders/Import/ImageUploadSection.tsx
import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, Image as ImageIcon, X, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/context/toast-hooks";
import { apiCall, endpoints } from "@/lib/api";
import { OCRProduct } from "@/types/product";

interface ImageUploadSectionProps {
  onOCRResult: (products: OCRProduct[]) => void;
  onClearOCR: () => void;
}

export const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  onOCRResult,
  onClearOCR,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [ocrResult, setOcrResult] = useState<OCRProduct[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showToast } = useToast();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        showToast('Vui lòng chọn file hình ảnh', 'error');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        showToast('File quá lớn. Vui lòng chọn file nhỏ hơn 10MB', 'error');
        return;
      }

      setSelectedFile(file);
      setError(null);
      setOcrResult(null);

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setOcrResult(null);
    setError(null);
    onClearOCR();

    // Clean up object URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.startsWith('image/')) {
        setSelectedFile(file);
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        setError(null);
        setOcrResult(null);
      } else {
        showToast('Vui lòng chọn file hình ảnh', 'error');
      }
    }
  };

  const handleOCRProcess = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);

      const response = await apiCall<{ products: OCRProduct[] }>(
        'POST',
        endpoints.orders.ocr,
        formData,
        { isFormData: true }
      );

      if (response.products && Array.isArray(response.products)) {
        setOcrResult(response.products);
        onOCRResult(response.products);
        showToast(`Đã xử lý thành công ${response.products.length} sản phẩm từ hình ảnh`, 'success');
      } else {
        throw new Error('Dữ liệu OCR không hợp lệ');
      }
    } catch (error: any) {
      console.error('OCR Error:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Có lỗi xảy ra khi xử lý hình ảnh';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          Upload hình ảnh đơn hàng (OCR)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!selectedFile ? (
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">
              Kéo thả hình ảnh vào đây hoặc click để chọn file
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
            >
              Chọn hình ảnh
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Image Preview */}
            <div className="relative">
              <img
                src={previewUrl || ''}
                alt="Preview"
                className="w-full max-w-md mx-auto rounded-lg border"
                style={{ maxHeight: '300px', objectFit: 'contain' }}
              />
              <Button
                onClick={handleRemoveFile}
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* OCR Actions */}
            <div className="flex gap-2">
              <Button
                onClick={handleOCRProcess}
                disabled={isProcessing}
                className="flex-1"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Đang xử lý OCR...
                  </>
                ) : (
                  'Xử lý OCR'
                )}
              </Button>
              <Button
                onClick={handleRemoveFile}
                variant="outline"
              >
                Chọn file khác
              </Button>
            </div>

            {/* Error Display */}
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <AlertCircle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            )}

            {/* Success Display */}
            {ocrResult && ocrResult.length > 0 && (
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
                <CheckCircle className="w-5 h-5" />
                <span>Đã xử lý thành công {ocrResult.length} sản phẩm</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};